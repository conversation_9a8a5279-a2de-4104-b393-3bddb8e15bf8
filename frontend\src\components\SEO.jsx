import { useEffect } from 'react';
import { useLocation } from 'react-router-dom';

const SEO = ({
  title,
  description,
  keywords,
  image,
  url,
  type = 'website',
  noindex = false
}) => {
  const location = useLocation();

  // Default SEO values
  const defaultTitle = "GenFreeAI | Best AI Tools In One Place For Free";
  const defaultDescription = "Remove backgrounds, upscale images to 4K, image-to-prompt generator, and more in one place for free.";
  const defaultKeywords = "Gen Free AI, AI editing toolkit, background remover, image upscaler, AI art generator, logo prompt generator, free AI tools, remove background online, upscale images 4K, AI image generator, text to image AI, AI design tools, free online tools, AI photo editor, image enhancement, creative AI tools, DALL-E alternative, Midjourney free, stable diffusion online, AI artwork generator, digital art creator";
  const defaultImage = "https://genfreeai.com/icon.png";
  const baseUrl = "https://genfreeai.com";

  // Use provided values or defaults
  const seoTitle = title || defaultTitle;
  const seoDescription = description || defaultDescription;
  const seoKeywords = keywords || defaultKeywords;
  const seoImage = image || defaultImage;
  const seoUrl = url || `${baseUrl}${location.pathname}`;

  useEffect(() => {
    // Update document title
    document.title = seoTitle;

    // Update basic meta tags
    updateMetaTag('name', 'description', seoDescription);
    updateMetaTag('name', 'keywords', seoKeywords);
    updateMetaTag('name', 'robots', noindex ? 'noindex, nofollow' : 'index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1');
    updateMetaTag('name', 'googlebot', 'index, follow');
    updateMetaTag('name', 'bingbot', 'index, follow');
    updateMetaTag('name', 'author', 'Gen Free AI');
    updateMetaTag('name', 'language', 'English');
    updateMetaTag('name', 'geo.region', 'US');
    updateMetaTag('name', 'theme-color', '#3B82F6');
    updateMetaTag('name', 'format-detection', 'telephone=no');
    updateMetaTag('name', 'apple-mobile-web-app-capable', 'yes');
    updateMetaTag('name', 'apple-mobile-web-app-status-bar-style', 'black-translucent');
    updateMetaTag('name', 'mobile-web-app-capable', 'yes');
    updateMetaTag('name', 'application-name', 'Gen Free AI');
    updateMetaTag('name', 'msapplication-TileColor', '#3B82F6');
    updateMetaTag('name', 'msapplication-config', '/browserconfig.xml');
    updateMetaTag('name', 'revisit-after', '7 days');
    updateMetaTag('name', 'rating', 'General');

    // Update Open Graph tags
    updateMetaTag('property', 'og:title', seoTitle);
    updateMetaTag('property', 'og:description', seoDescription);
    updateMetaTag('property', 'og:image', seoImage);
    updateMetaTag('property', 'og:image:width', '1200');
    updateMetaTag('property', 'og:image:height', '630');
    updateMetaTag('property', 'og:image:alt', '100% Free AI Image Generator - Gen Free AI');
    updateMetaTag('property', 'og:url', seoUrl);
    updateMetaTag('property', 'og:type', type);
    updateMetaTag('property', 'og:site_name', 'Gen Free AI');
    updateMetaTag('property', 'og:locale', 'en_US');

    // Update Twitter tags
    updateMetaTag('name', 'twitter:card', 'summary_large_image');
    updateMetaTag('name', 'twitter:title', seoTitle);
    updateMetaTag('name', 'twitter:description', seoDescription);
    updateMetaTag('name', 'twitter:image', seoImage);
    updateMetaTag('name', 'twitter:image:alt', 'Gen Free AI - Free AI Image Generator');
    updateMetaTag('name', 'twitter:site', '@genfreeai');
    updateMetaTag('name', 'twitter:creator', '@genfreeai');

    // Update canonical URL
    updateCanonicalUrl(seoUrl);

    // Add structured data
    addStructuredData();

  }, [seoTitle, seoDescription, seoKeywords, seoImage, seoUrl, type, noindex]);

  const updateMetaTag = (attribute, value, content) => {
    let element = document.querySelector(`meta[${attribute}="${value}"]`);
    if (element) {
      element.setAttribute('content', content);
    } else {
      element = document.createElement('meta');
      element.setAttribute(attribute, value);
      element.setAttribute('content', content);
      document.head.appendChild(element);
    }
  };

  const updateCanonicalUrl = (url) => {
    let element = document.querySelector('link[rel="canonical"]');
    if (element) {
      element.setAttribute('href', url);
    } else {
      element = document.createElement('link');
      element.setAttribute('rel', 'canonical');
      element.setAttribute('href', url);
      document.head.appendChild(element);
    }
  };

  const addStructuredData = () => {
    // Remove existing structured data
    const existingScript = document.querySelector('script[type="application/ld+json"]');
    if (existingScript) {
      existingScript.remove();
    }

    // Add comprehensive structured data
    const structuredData = {
      "@context": "https://schema.org",
      "@graph": [
        {
          "@type": "WebApplication",
          "@id": `${baseUrl}/#webapp`,
          "name": "Gen Free AI",
          "alternateName": "Gen Free AI Image Generator",
          "description": seoDescription,
          "url": baseUrl,
          "applicationCategory": "DesignApplication",
          "operatingSystem": "Web Browser",
          "browserRequirements": "Requires JavaScript. Requires HTML5.",
          "softwareVersion": "1.0",
          "datePublished": "2024-01-01",
          "dateModified": new Date().toISOString().split('T')[0],
          "inLanguage": "en-US",
          "isAccessibleForFree": true,
          "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "USD",
            "availability": "https://schema.org/InStock",
            "validFrom": "2024-01-01"
          },
          "creator": {
            "@type": "Organization",
            "@id": `${baseUrl}/#organization`
          },
          "featureList": [
            "Free AI Image Generation",
            "Text to Image Conversion",
            "High Quality Images",
            "No Registration Required",
            "Instant Generation",
            "Commercial Use Rights"
          ],
          "screenshot": seoImage,
          "aggregateRating": {
            "@type": "AggregateRating",
            "ratingValue": "4.8",
            "ratingCount": "1250",
            "bestRating": "5",
            "worstRating": "1",
            "reviewCount": "850"
          },
          "award": "Best Free AI Image Generator 2024",
          "hasPart": [
            {
              "@type": "WebPageElement",
              "isAccessibleForFree": true,
              "name": "Image Generator"
            },
            {
              "@type": "WebPageElement",
              "isAccessibleForFree": true,
              "name": "Image History"
            }
          ],
          "mainEntityOfPage": {
            "@type": "WebPage",
            "@id": `${baseUrl}${location.pathname}`
          }
        },
        {
          "@type": "Organization",
          "@id": `${baseUrl}/#organization`,
          "name": "Gen Free AI",
          "url": baseUrl,
          "logo": {
            "@type": "ImageObject",
            "url": `${baseUrl}/icon.png`,
            "width": 512,
            "height": 512
          },
          "sameAs": [
            "https://facebook.com/genfreeai",
            "https://twitter.com/genfreeai",
            "https://instagram.com/genfreeai",
            "https://linkedin.com/company/genfreeai",
            "https://tiktok.com/@genfreeai",
            "https://youtube.com/@genfreeai"
          ],
          "contactPoint": {
            "@type": "ContactPoint",
            "email": "<EMAIL>",
            "contactType": "customer service"
          }
        },
        {
          "@type": "WebSite",
          "@id": `${baseUrl}/#website`,
          "url": baseUrl,
          "name": "Gen Free AI",
          "description": seoDescription,
          "publisher": {
            "@id": `${baseUrl}/#organization`
          },
          "breadcrumb": {
            "@type": "BreadcrumbList",
            "itemListElement": [
              {
                "@type": "ListItem",
                "position": 1,
                "name": "Home",
                "item": baseUrl
              },
              {
                "@type": "ListItem",
                "position": 2,
                "name": location.pathname.split('/')[1] || 'Home',
                "item": `${baseUrl}${location.pathname}`
              }
            ]
          },
          "potentialAction": [
            {
              "@type": "SearchAction",
              "target": {
                "@type": "EntryPoint",
                "urlTemplate": `${baseUrl}/search?q={search_term_string}`
              },
              "query-input": "required name=search_term_string"
            }
          ],
          "inLanguage": "en-US"
        }
      ]
    };

    // Add FAQ structured data if on FAQ page
    if (location.pathname === '/faq') {
      structuredData["@graph"].push({
        "@type": "FAQPage",
        "mainEntity": [
          {
            "@type": "Question",
            "name": "Is Gen Free AI really free?",
            "acceptedAnswer": {
              "@type": "Answer",
              "text": "Yes! Gen Free AI is completely free to use. There are no hidden fees, subscriptions, or premium tiers. We believe AI creativity should be accessible to everyone."
            }
          },
          {
            "@type": "Question",
            "name": "How do I generate an image?",
            "acceptedAnswer": {
              "@type": "Answer",
              "text": "Simply go to our Generate page, type a detailed description of what you want to create in the prompt box, and click 'Generate Image'. Our AI will process your request and create a unique image based on your description."
            }
          },
          {
            "@type": "Question",
            "name": "What image quality do you provide?",
            "acceptedAnswer": {
              "@type": "Answer",
              "text": "All generated images are provided in JPG format with a resolution of (1:1,3:4,9:16,4:3,16:9,2:3,3:2) pixels, ensuring high quality for both digital and print use."
            }
          },
          {
            "@type": "Question",
            "name": "Can I use generated images commercially?",
            "acceptedAnswer": {
              "@type": "Answer",
              "text": "Yes! All images generated through Gen Free AI can be used for commercial purposes. You have full rights to the images you create."
            }
          }
        ]
      });
    }

    const script = document.createElement('script');
    script.type = 'application/ld+json';
    script.textContent = JSON.stringify(structuredData);
    document.head.appendChild(script);
  };

  return null; // This component doesn't render anything
};

// Page-specific SEO configurations
export const pageSEO = {
  home: {
    title: "GenFreeAI | Best AI Tools In One Place For Free",
    description: "Remove backgrounds, upscale images to 4K, image-to-prompt generator, and more in one place for free.",
    keywords: ""
  },
  history: {
    title: "Image History - View & Download Your Generated AI Images | Gen Free AI",
    description: "📚 Browse, manage, and download your previously generated AI images with Gen Free AI. Search through your AI-created artwork collection with our convenient history feature. Access all your AI art creations in one place.",
    keywords: `  
BG Remover,Remove background from image online
Erase Background,Make image background transparent free
Transparent BG,Automatic background eraser tool
Photo Cutout,Remove white background from logo
Background Editor,Instant photo background remover, 
Image Upscaler,Upscale low resolution images online
Photo Enhancer,Make blurry pictures clear AI
4K Upscaler,Increase image quality without losing detail
Sharpen Image,AI photo restoration tool
Resizer,Convert low quality image to high quality,  
Image to Prompt,Extract prompt from image AI
Reverse Prompt,Get text prompt from picture
Photo to Text,Reverse engineer AI art prompts
Prompt Finder,Convert image to Midjourney prompt
Visual to Text,Analyze image styles for prompts,  
Logo AI,AI prompt generator for logos
Brand Design,Best prompts for vector logos
Logo Maker,Create business logo description for AI
Icon Generator,Minimalist logo prompt ideas
Startup Logo,Text to logo prompt builder,  
Prompt Builder,Turn simple text into detailed prompts
Prompt Helper,AI art prompt expander
Better Prompts,Optimize prompts for Stable Diffusion
Prompt Magic,Professional AI image description writer
Prompt Engineer,Generate highly detailed art prompts ,  
AI Art,Free AI image generator from text
Image Generator,Best text to image AI tool
AI Drawing,Generate realistic photos with AI
Generative AI,Create digital art online free
Text to Image,AI photo generator for beginners, AI tools, Ai tools directori, free ai directtori `
  },
  about: {
    title: "Learn About GenFreeAI",
    description: "🚀 Learn about Gen Free AI, our mission to democratize AI image generation, and how we're making creative AI tools accessible to everyone worldwide. Contact <NAME_EMAIL> for support, partnerships, and collaboration opportunities.",
    keywords: "about gen free ai, ai image generator, free tools, mission, contact, <EMAIL>, AI technology, creative tools, democratize AI, image generation, AI company, artificial intelligence startup, free AI platform, AI accessibility, creative technology, AI innovation, machine learning company"
  },
  faq: {
    title: "FAQ - Frequently Asked Questions | GenFreeAI ",
    description: "❓ Find comprehensive answers to common questions about Gen Free AI. Learn how to generate images, download them, understand our privacy policies, troubleshoot issues, and get technical support. Complete help guide for AI image generation.",
    keywords: "Gen Free AI FAQ, frequently asked questions, AI image generator help, how to generate images, image download, privacy policy, technical support, troubleshooting, AI art help, image generation guide, AI tool tutorial, help center, user guide, AI image tips, generation problems, support documentation"
  },
  terms: {
    title: "Terms and Conditions | GenFreeAI",
    description: "📋 Read our comprehensive terms and conditions for using Gen Free AI. Learn about usage rights, content policies, commercial use permissions, and legal information for our free AI image generation service.",
    keywords: "Gen Free AI terms, terms and conditions, AI image generator terms, usage policy, legal information, AI art terms of service, commercial use rights, content policy, user agreement, legal compliance, AI tool terms, image rights, intellectual property, usage guidelines"
  },
  backgroundRemove: {
    title: "Best Background Remover Online For Free | GenFreeAI",
    description: "Remove backgrounds from images for free using AI. Perfect for products, portraits & graphics. No signup, no limits, instant results. 100% free.",
    keywords: "free background remover, remove background online, AI background removal, background eraser, transparent background, image background remover, photo background remover, background removal tool, AI photo editor, remove bg, background cutter, image editor, photo editing tool, transparent PNG, background delete, AI image processing"
  },
  upscaleImage: {
    title: "Best Image Upscaler Online For Free | 4K Upscaling",
    description: "Free AI image upscaler online - Enhance image quality and resolution instantly with AI. Upscale images up to 4x without quality loss. No watermarks, no signup required.",
    keywords: "free image upscaler, AI image enhancer, upscale image online, enhance image quality, image resolution enhancer, AI photo upscaler, image enlarger, photo enhancer, 4K upscaler, image quality improver, AI image enhancement, photo quality enhancer, image super resolution, AI upscaling, enhance photo resolution"
  },
  imageToPrompt: {
    title: "Best Image to Prompt Generator Online For Free",
    description: "Free Image to Prompt tool that converts images into detailed AI prompts. Perfect for recreating images in Midjourney, Stable Diffusion, or any AI art generator. Try it now!",
    keywords: "image to prompt, AI image analysis, prompt generator, image description, AI art prompts, reverse engineering prompts"
  },
  logoPrompt: {
    title: "Free AI logo design prompt generator | GenFreeAI",
    description: "Generate professional logo design prompts with AI. Create detailed descriptions for logo creation in any AI tool. Perfect for branding and business identity.",
    keywords: "logo prompt generator, AI logo design, logo description generator, brand identity prompts, business logo prompts, logo design AI, branding prompts, logo creation tool"
  },
  imagePromptGenerator: {
    title: "AI Prompt Enhancer - Free Online",
    description: "Master image prompt creation with our AI-powered tools. Generate and optimize image prompts for Midjourney, Flux, Stable Diffusion and more.",
    keywords: "AI prompt enhancer, image prompt generator, Midjourney prompts, Flux prompts, Stable Diffusion prompts, AI prompt optimizer, prompt engineering, AI art prompts, image generation prompts, prompt creator, AI prompt tools, prompt enhancement, creative prompts, AI prompt builder, prompt optimization"
  }
};

export default SEO;
