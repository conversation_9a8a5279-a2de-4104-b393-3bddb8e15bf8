const API_BASE_URL = `${import.meta.env.VITE_API_BASE_URL}/api`;

// Get all tools with their stats
export const getAllTools = async () => {
  try {
    const response = await fetch(`${API_BASE_URL}/tools`);
    const data = await response.json();
    
    if (!response.ok) {
      throw new Error(data.message || 'Failed to fetch tools');
    }
    
    return data.data;
  } catch (error) {
    console.error('Error fetching tools:', error);
    throw error;
  }
};

// Get a specific tool by slug
export const getToolBySlug = async (slug) => {
  try {
    const response = await fetch(`${API_BASE_URL}/tools/${slug}`);
    const data = await response.json();
    
    if (!response.ok) {
      throw new Error(data.message || 'Failed to fetch tool');
    }
    
    return data.data;
  } catch (error) {
    console.error('Error fetching tool:', error);
    throw error;
  }
};

// Increment view count for a tool
export const incrementToolViews = async (slug) => {
  try {
    const response = await fetch(`${API_BASE_URL}/tools/${slug}/view`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
    });
    
    const data = await response.json();
    
    if (!response.ok) {
      throw new Error(data.message || 'Failed to update view count');
    }
    
    return data.data;
  } catch (error) {
    console.error('Error updating view count:', error);
    throw error;
  }
};

// Increment or decrement love count for a tool
export const incrementToolLoves = async (slug, increment = 1) => {
  try {
    const response = await fetch(`${API_BASE_URL}/tools/${slug}/love`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ increment }),
    });

    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.message || 'Failed to update love count');
    }

    return data.data;
  } catch (error) {
    console.error('Error updating love count:', error);
    throw error;
  }
};

// Format number for display (e.g., 1234 -> 1.2K)
export const formatNumber = (num) => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M';
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K';
  }
  return num.toString();
};
