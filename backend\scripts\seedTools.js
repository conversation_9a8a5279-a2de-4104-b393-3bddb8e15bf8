const mongoose = require('mongoose');
const Tool = require('../models/Tool');
require('dotenv').config();

const tools = [
  {
    name: 'Image Generator',
    slug: 'image-generator',
    description: 'Create stunning AI-generated images from text prompts',
    icon: 'Wand2',
    views: 2700,
    loves: 0,
    route: '/generate'
  },
  {
    name: 'Background Removal',
    slug: 'background-removal',
    description: 'Remove backgrounds from images instantly with AI precision',
    icon: 'Scissors',
    views: 1480,
    loves: 0,
    route: '/background-remove'
  },
  {
    name: 'Image Upscaling',
    slug: 'image-upscaling',
    description: 'Enhance image resolution up to 4k using AI',
    icon: 'Search',
    views: 1250,
    loves: 0,
    route: '/upscale-image'
  },
  {
    name: 'Image to Prompt',
    slug: 'image-to-prompt',
    description: 'Generate detailed prompts from images',
    icon: 'ImageIcon',
    views: 807,
    loves: 0,
    route: '/image-to-prompt'
  },
  {
    name: 'Logo Prompt Generator',
    slug: 'logo-prompt-generator',
    description: 'Generate professional logo design prompts for your brand',
    icon: 'Palette',
    views: 408,
    loves: 0,
    route: '/logo-prompt'
  },
  {
    name: 'Image Prompt Generator',
    slug: 'image-prompt-generator',
    description: 'Transform basic ideas into detailed prompts',
    icon: 'FileText',
    views: 2340,
    loves: 0,
    route: '/image-prompt-generator'
  }
];

async function seedTools() {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('✅ Connected to MongoDB');

    // Clear existing tools
    await Tool.deleteMany({});
    console.log('🗑️ Cleared existing tools');

    // Insert new tools
    const insertedTools = await Tool.insertMany(tools);
    console.log(`✅ Inserted ${insertedTools.length} tools`);

    // Display inserted tools
    insertedTools.forEach(tool => {
      console.log(`📊 ${tool.name}: ${tool.views.toLocaleString()} views, ${tool.loves} loves`);
    });

    console.log('🎉 Tool seeding completed successfully!');
    process.exit(0);
  } catch (error) {
    console.error('❌ Error seeding tools:', error);
    process.exit(1);
  }
}

// Run the seeding function
seedTools();
