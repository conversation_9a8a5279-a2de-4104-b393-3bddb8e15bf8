import React from 'react';
import { ArrowLeft, Calendar, Clock, Tag, Share2, BookO<PERSON>, Wand2, TrendingUp } from 'lucide-react';
import { useNavigate, Link } from 'react-router-dom';
import SEO from './SEO';
import AdvertisementSpace from './AdvertisementSpace';

const BlogPost = ({ post, onBack, allPosts = [] }) => {
  const navigate = useNavigate();

  // Helper function to create URL-friendly slugs
  const createSlug = (title) => {
    return title
      .toLowerCase()
      .replace(/[^\w\s-]/g, '') // Remove special characters
      .replace(/\s+/g, '-') // Replace spaces with hyphens
      .replace(/--+/g, '-') // Replace multiple hyphens with single hyphen
      .trim(); // Trim leading/trailing spaces
  };

  // Generate dynamic quick navigation links
  const generateQuickNavigation = () => {
    if (!allPosts || allPosts.length === 0) {
      // Fallback to static links if no posts provided
      return [
        {
          title: "AI Image Generator",
          url: "/generate",
          icon: Wand2,
          isInternal: true
        },
        {
          title: "Blog Home",
          url: "/blog",
          icon: BookOpen,
          isInternal: true
        }
      ];
    }

    // Get current post index
    const currentPostIndex = allPosts.findIndex(p => p.id === post?.id);

    // Create navigation links from available posts
    const navLinks = [];

    // Add featured/recent posts (excluding current post)
    const otherPosts = allPosts.filter(p => p.id !== post?.id);

    // Add up to 4 other posts, prioritizing featured ones
    const featuredPosts = otherPosts.filter(p => p.featured).slice(0, 2);
    const recentPosts = otherPosts.filter(p => !p.featured).slice(0, 2);

    [...featuredPosts, ...recentPosts].slice(0, 4).forEach(blogPost => {
      navLinks.push({
        title: blogPost.title.length > 50 ? blogPost.title.substring(0, 50) + '...' : blogPost.title,
        url: `/blog/${createSlug(blogPost.title)}`,
        icon: TrendingUp,
        isInternal: true,
        category: blogPost.category
      });
    });

    // Always add essential link
    navLinks.push({
      title: "Generate AI Images",
      url: "/generate",
      icon: Wand2,
      isInternal: true
    });

    return navLinks;
  };

  const quickNavLinks = generateQuickNavigation();

  if (!post) {
    return (
      <div className="min-h-screen bg-gray-900 py-8">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center py-16">
            <h1 className="text-2xl font-bold text-white mb-4">Post Not Found</h1>
            <p className="text-gray-400 mb-8">The blog post you're looking for doesn't exist.</p>
            <button
              onClick={() => navigate('/')}
              className="inline-flex items-center gap-2 px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors"
            >
              <ArrowLeft className="w-4 h-4" />
              Back to Home
            </button>
          </div>
        </div>
      </div>
    );
  }

  const handleShare = async () => {
    // Get the current URL which now includes the blog post slug
    const shareUrl = window.location.href;
    
    if (navigator.share) {
      try {
        await navigator.share({
          title: post.title,
          text: post.excerpt,
          url: shareUrl,
        });
      } catch (error) {
        console.log('Error sharing:', error);
      }
    } else {
      // Fallback: copy to clipboard
      navigator.clipboard.writeText(shareUrl);
      // You could show a toast notification here
      alert('Link copied to clipboard!');
    }
  };

  return (
    <div className="min-h-screen bg-gray-900 py-8">
      {/* SEO */}
      <SEO
        title={`${post.title} | Gen Free AI Blog`}
        description={post.excerpt}
        keywords={`${post.category}, AI image generation, ${post.title.toLowerCase()}`}
        url={window.location.href}
        type="article"
      />

      <div className="max-w-7xl mx-auto px-3 sm:px-4 md:px-6 lg:px-8">
        {/* Back Button */}
        <button
          onClick={onBack || (() => navigate('/blog'))}
          className="inline-flex items-center gap-2 text-blue-400 hover:text-blue-300 font-medium mb-6 sm:mb-8 transition-colors"
        >
          <ArrowLeft className="w-4 h-4" />
          Back to Blog
        </button>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-4 sm:gap-6 lg:gap-8">
          {/* Main Content */}
          <article className="lg:col-span-3 min-w-0 overflow-hidden">
            {/* Article Header */}
            <header className="mb-8">
              <div className="flex items-center gap-4 text-sm text-gray-400 mb-4">
                <span className="inline-flex items-center gap-1 px-3 py-1 rounded-full bg-blue-900 text-blue-200 font-medium">
                  <Tag className="w-3 h-3" />
                  {post.category}
                </span>
                <div className="flex items-center gap-1">
                  <Calendar className="w-4 h-4" />
                  {post.date}
                </div>
                <div className="flex items-center gap-1">
                  <Clock className="w-4 h-4" />
                  {post.readTime}
                </div>
              </div>

              <h1 className="text-3xl md:text-4xl font-bold text-white mb-4 leading-tight">
                {post.title}
              </h1>

              <p className="text-xl text-gray-300 leading-relaxed mb-6">
                {post.excerpt}
              </p>

              {/* Share Button */}
              <button
                onClick={handleShare}
                className="inline-flex items-center gap-2 px-4 py-2 bg-gray-800 hover:bg-gray-700 text-gray-300 rounded-lg font-medium transition-colors"
              >
                <Share2 className="w-4 h-4" />
                Share Article
              </button>
            </header>

            {/* Article Content */}
            <div
              className="prose prose-sm sm:prose-base lg:prose-lg prose-invert max-w-none overflow-hidden
                prose-headings:text-white prose-headings:break-words
                prose-p:text-gray-300 prose-p:break-words
                prose-li:text-gray-300 prose-li:break-words
                prose-strong:text-white
                prose-a:text-blue-400 prose-a:break-words
                prose-code:text-purple-400 prose-code:break-all
                prose-pre:bg-gray-800 prose-pre:overflow-x-auto
                prose-blockquote:border-blue-400
                prose-table:text-sm prose-table:overflow-x-auto
                prose-img:max-w-full prose-img:h-auto"
              dangerouslySetInnerHTML={{ __html: post.content }}
            />

            {/* Article Footer */}
            <footer className="mt-12 pt-8 border-t border-gray-700">
              <div className="flex items-center justify-between">
                <div className="text-sm text-gray-400">
                  Published on {post.date}
                </div>
                <button
                  onClick={handleShare}
                  className="inline-flex items-center gap-2 text-blue-400 hover:text-blue-300 font-medium transition-colors"
                >
                  <Share2 className="w-4 h-4" />
                  Share this article
                </button>
              </div>
            </footer>
          </article>

          {/* Sidebar */}
          <aside className="lg:col-span-1 space-y-4 sm:space-y-6 min-w-0">
            <div className="sticky top-20">
              <AdvertisementSpace />
              
              {/* Dynamic Quick Navigation */}
              <div className="bg-gray-800 rounded-lg shadow-md p-6 mt-6">
                <h3 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
                  <BookOpen className="w-5 h-5 text-blue-400" />
                  Quick Navigation
                </h3>
                <nav className="space-y-3">
                  {quickNavLinks.map((link, index) => {
                    const Icon = link.icon;

                    if (link.isInternal) {
                      return (
                        <Link
                          key={index}
                          to={link.url}
                          className="flex items-center gap-3 p-3 rounded-lg bg-gray-700/50 hover:bg-gray-700 text-sm text-gray-300 hover:text-white transition-all duration-200 group"
                        >
                          <Icon className="w-4 h-4 text-blue-400 group-hover:text-blue-300 flex-shrink-0" />
                          <div className="flex-1 min-w-0">
                            <div className="truncate font-medium">{link.title}</div>
                            {link.category && (
                              <div className="text-xs text-gray-500 capitalize">{link.category}</div>
                            )}
                          </div>
                        </Link>
                      );
                    } else {
                      return (
                        <a
                          key={index}
                          href={link.url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="flex items-center gap-3 p-3 rounded-lg bg-gray-700/50 hover:bg-gray-700 text-sm text-gray-300 hover:text-white transition-all duration-200 group"
                        >
                          <Icon className="w-4 h-4 text-blue-400 group-hover:text-blue-300 flex-shrink-0" />
                          <div className="flex-1 min-w-0">
                            <div className="truncate font-medium">{link.title}</div>
                            {link.category && (
                              <div className="text-xs text-gray-500 capitalize">{link.category}</div>
                            )}
                          </div>
                        </a>
                      );
                    }
                  })}
                </nav>

                {/* Additional Quick Actions */}
                <div className="mt-4 pt-4 border-t border-gray-700">
                  <div className="text-xs text-gray-500 mb-2">Quick Actions</div>
                  <Link
                    to="/generate"
                    className="block w-full px-4 py-3 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white text-sm font-medium rounded-lg transition-all duration-200 text-center"
                  >
                    Generate AI Art
                  </Link>
                </div>
              </div>

            
            </div>
          </aside>
        </div>
      </div>
    </div>
  );
};

export default BlogPost;
