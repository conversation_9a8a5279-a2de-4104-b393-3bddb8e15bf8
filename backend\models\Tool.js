const mongoose = require('mongoose');

const toolSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    unique: true,
    trim: true
  },
  slug: {
    type: String,
    required: true,
    unique: true,
    trim: true
  },
  description: {
    type: String,
    required: true,
    trim: true
  },
  icon: {
    type: String,
    required: true
  },
  views: {
    type: Number,
    default: 0,
    min: 0
  },
  loves: {
    type: Number,
    default: 0,
    min: 0
  },
  route: {
    type: String,
    required: true,
    trim: true
  },
  isActive: {
    type: Boolean,
    default: true
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true
});

// Index for better query performance
toolSchema.index({ slug: 1 });
toolSchema.index({ views: -1 });
toolSchema.index({ loves: -1 });
toolSchema.index({ isActive: 1 });

// Pre-save middleware to update updatedAt
toolSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

module.exports = mongoose.model('Tool', toolSchema);
