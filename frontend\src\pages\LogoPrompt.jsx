import React, { useState } from 'react';
import { Loader2, <PERSON><PERSON><PERSON><PERSON>, <PERSON>ert<PERSON>ircle, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>les, Cloud, Building2, MessageCircle, ChevronDown } from 'lucide-react';
import axios from 'axios';
import SEO, { pageSEO } from '../components/SEO';
import ParticleBackground from '../components/ParticleBackground';

// FeatureCard Component
function FeatureCard({ icon, title, description }) {
  return (
    <div className="bg-gray-800/30 backdrop-blur-md rounded-2xl p-6 border border-gray-700/30 hover:border-purple-500/30 transition-all duration-300 group">
      <div className="text-4xl mb-4 group-hover:scale-110 transition-transform duration-300">
        {icon}
      </div>
      <h3 className="text-xl font-semibold text-white mb-3 group-hover:text-purple-300 transition-colors duration-300">
        {title}
      </h3>
      <p className="text-gray-400 leading-relaxed">
        {description}
      </p>
    </div>
  );
}

// FAQItem Component
function FAQItem({ question, answer, isOpen, onClick }) {
  return (
    <div className="bg-gray-800/30 backdrop-blur-md rounded-2xl border border-gray-700/30 overflow-hidden hover:border-purple-500/30 transition-all duration-300">
      <button
        onClick={onClick}
        className="w-full px-6 py-5 text-left flex items-center justify-between hover:bg-gray-700/20 transition-colors duration-200"
      >
        <h3 className="text-lg font-semibold text-white pr-4">{question}</h3>
        <ChevronDown
          className={`w-5 h-5 text-purple-400 transition-transform duration-300 flex-shrink-0 ${
            isOpen ? 'rotate-180' : ''
          }`}
        />
      </button>
      {isOpen && (
        <div className="px-6 pb-5">
          <div className="pt-2 border-t border-gray-700/30">
            <p className="text-gray-300 leading-relaxed">{answer}</p>
          </div>
        </div>
      )}
    </div>
  );
}

const LogoPrompt = () => {
  const [brandName, setBrandName] = useState('');
  const [businessDescription, setBusinessDescription] = useState('');
  const [logoPrompts, setLogoPrompts] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(false);
  const [copied, setCopied] = useState(false);

  // FAQ State
  const [openFAQ, setOpenFAQ] = useState(0);

  const generateLogoPrompts = async () => {
    if (!brandName.trim()) {
      setError('Please enter your brand name');
      return;
    }

    if (!businessDescription.trim()) {
      setError('Please enter your business description');
      return;
    }

    setIsProcessing(true);
    setError(null);
    setSuccess(false);

    try {
      const response = await axios.post(`${import.meta.env.VITE_API_BASE_URL}/api/ai/logo-prompt`, {
        brandName: brandName.trim(),
        businessDescription: businessDescription.trim()
      });

      if (response.data.success) {
        setLogoPrompts(response.data.logoPrompts);
        setSuccess(true);
      } else {
        setError(response.data.error || 'Failed to generate logo prompts');
      }
    } catch (error) {
      console.error('Error generating logo prompts:', error);
      setError(error.response?.data?.error || 'Failed to generate logo prompts');
    } finally {
      setIsProcessing(false);
    }
  };

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(logoPrompts);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      setError('Failed to copy to clipboard');
    }
  };

  const clearAll = () => {
    setBrandName('');
    setBusinessDescription('');
    setLogoPrompts('');
    setError(null);
    setSuccess(false);
    setCopied(false);
  };

  return (
    <div className="relative min-h-screen">
      {/* SEO */}
      <SEO
        title={pageSEO.logoPrompt.title}
        description={pageSEO.logoPrompt.description}
        keywords={pageSEO.logoPrompt.keywords}
      />

      {/* Particle Background */}
      <ParticleBackground />

      {/* Background gradient */}
      <div className="absolute inset-0 bg-gradient-to-br from-purple-900/20 via-blue-900/20 to-indigo-900/20 -z-10"></div>

      {/* Subtle pattern overlay */}
      <div className="absolute inset-0 opacity-10 -z-5" style={{
        backgroundImage: `radial-gradient(circle at 25% 25%, rgba(59, 130, 246, 0.3) 0%, transparent 50%),
                         radial-gradient(circle at 75% 75%, rgba(147, 51, 234, 0.3) 0%, transparent 50%)`
      }}></div>

      {/* Content */}
      <div className="relative z-10 py-8 px-4 sm:px-6 lg:px-8 max-w-6xl mx-auto">
        {/* Header */}
        <div className="text-center mb-8 sm:mb-12">
          <div className="flex items-center justify-center gap-3 mb-4">
            <div className="p-3 bg-gradient-to-br from-purple-600 to-pink-600 rounded-2xl shadow-lg">
              <Palette className="w-8 h-8 text-white" />
            </div>
            <h1 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-white">
              Logo Prompt Generator
            </h1>
          </div>
          <p className="text-lg sm:text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
            Transform your brand vision into creative logo design prompts. Get 5 unique AI-optimized prompts 
            for tools like Midjourney, DALL-E, and Stable Diffusion.
          </p>
        </div>

        {/* Error Message */}
        {error && (
          <div className="mb-6 p-4 bg-red-900/50 border border-red-700/50 rounded-xl flex items-center gap-3">
            <AlertCircle className="w-5 h-5 text-red-400 flex-shrink-0" />
            <p className="text-red-200 text-sm sm:text-base">{error}</p>
          </div>
        )}

        {/* Success Message */}
        {success && (
          <div className="mb-6 p-4 bg-green-900/50 border border-green-700/50 rounded-xl flex items-center gap-3">
            <CheckCircle className="w-5 h-5 text-green-400 flex-shrink-0" />
            <p className="text-green-200 text-sm sm:text-base">
              Successfully generated 5 logo design prompts! Copy and use them in your favorite AI image generator.
            </p>
          </div>
        )}

        {/* Main Content - Side by Side Layout with equal division */}
        <div className="grid lg:grid-cols-2 gap-6">
          {/* Input Section - Left Side */}
          <div className="bg-gray-800/50 backdrop-blur-md rounded-2xl sm:rounded-3xl p-4 sm:p-6 lg:p-8 border border-gray-700/50 shadow-2xl">
            <div className="flex items-center gap-2 mb-6">
              <Building2 className="w-5 h-5 text-purple-400" />
              <h2 className="text-lg sm:text-xl font-semibold text-white">Brand Information</h2>
            </div>

            <div className="space-y-6">
              {/* Brand Name Input */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Brand/Product Name *
                </label>
                <input
                  type="text"
                  value={brandName}
                  onChange={(e) => setBrandName(e.target.value)}
                  placeholder="e.g., EcoBlend Coffee, TechFlow Solutions, etc."
                  className="w-full px-4 py-3 bg-gray-700/50 border border-gray-600/50 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200"
                  maxLength={50}
                />
                <div className="text-right text-xs text-gray-400 mt-1">
                  {brandName.length}/50
                </div>
              </div>

              {/* Business Description Input */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Business Description *
                </label>
                <textarea
                  value={businessDescription}
                  onChange={(e) => setBusinessDescription(e.target.value)}
                  placeholder="Describe your business, industry, values, and target audience. e.g., 'An eco-friendly coffee shop specializing in organic blends and sustainable practices, targeting environmentally conscious millennials.'"
                  rows={6}
                  className="w-full px-4 py-3 bg-gray-700/50 border border-gray-600/50 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200 resize-none"
                  maxLength={500}
                />
                <div className="text-right text-xs text-gray-400 mt-1">
                  {businessDescription.length}/500
                </div>
              </div>

              {/* Generate Button */}
              <div className="flex flex-col gap-3">
                <button
                  onClick={generateLogoPrompts}
                  disabled={isProcessing || !brandName.trim() || !businessDescription.trim()}
                  className="w-full py-3 sm:py-4 bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 disabled:from-gray-600 disabled:to-gray-700 text-white rounded-2xl font-semibold transition-all duration-200 flex items-center justify-center gap-2 cursor-pointer disabled:cursor-not-allowed shadow-lg text-sm sm:text-base"
                >
                  {isProcessing ? (
                    <>
                      <Loader2 className="w-4 h-4 sm:w-5 sm:h-5 animate-spin" />
                      Generating Prompts...
                    </>
                  ) : (
                    <>
                      <Sparkles className="w-4 h-4 sm:w-5 sm:h-5" />
                      Generate Logo Prompts
                    </>
                  )}
                </button>

                {logoPrompts && (
                  <button
                    onClick={clearAll}
                    className="w-full py-3 bg-gray-600/80 hover:bg-gray-700/80 text-white rounded-2xl font-semibold transition-all duration-200 text-sm sm:text-base"
                  >
                    Clear All
                  </button>
                )}
              </div>
            </div>
          </div>

          {/* Output Section - Right Side */}
          <div className="bg-gray-800/50 backdrop-blur-md rounded-2xl sm:rounded-3xl p-4 sm:p-6 lg:p-8 border border-gray-700/50 shadow-2xl">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center gap-2">
                <Sparkles className="w-5 h-5 text-purple-400" />
                <h2 className="text-lg sm:text-xl font-semibold text-white">Generated Logo Prompts</h2>
              </div>

              {logoPrompts && (
                <button
                  onClick={copyToClipboard}
                  className="px-3 py-2 bg-purple-600/80 hover:bg-purple-700/80 text-white rounded-lg transition-all duration-200 flex items-center gap-2 text-sm"
                >
                  <Copy className="w-4 h-4" />
                  {copied ? 'Copied!' : 'Copy All'}
                </button>
              )}
            </div>

            <div className="h-[500px] bg-gray-900/50 rounded-xl p-4 border border-gray-600/30 overflow-auto">
              {isProcessing ? (
                <div className="flex flex-col items-center justify-center h-full text-gray-400">
                  <div className="typewriter mb-6">
                    <div className="slide"><i></i></div>
                    <div className="paper"></div>
                    <div className="keyboard"></div>
                  </div>
                  <p className="text-lg font-medium mb-2">Generating Creative Logo Prompts</p>
                  <p className="text-sm text-center">
                    AI is analyzing your brand and creating unique design prompts...
                  </p>
                </div>
              ) : logoPrompts ? (
                <div className="space-y-4">
                  <pre className="text-gray-200 text-sm sm:text-base leading-relaxed whitespace-pre-wrap font-mono">
                    {logoPrompts}
                  </pre>
                </div>
              ) : (
                <div className="flex flex-col items-center justify-center h-full text-gray-500">
                  <Palette className="w-12 h-12 mb-4" />
                  <p className="text-lg font-medium mb-2">Ready to Create Logo Prompts</p>
                  <p className="text-sm text-center">
                    Enter your brand name and business description to generate 5 unique logo design prompts.
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Tips Section */}
        {!logoPrompts && (
          <div className="mt-8 bg-blue-900/20 backdrop-blur-md rounded-2xl p-6 border border-blue-700/30">
            <h3 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
              <Sparkles className="w-5 h-5 text-blue-400" />
              Pro Tips for Better Results
            </h3>
            <ul className="space-y-2 text-gray-300 text-sm">
              <li className="flex items-start gap-2">
                <span className="text-blue-400 mt-1">•</span>
                <span>Be specific about your industry and target audience</span>
              </li>
              <li className="flex items-start gap-2">
                <span className="text-blue-400 mt-1">•</span>
                <span>Mention your brand values (eco-friendly, innovative, luxury, etc.)</span>
              </li>
              <li className="flex items-start gap-2">
                <span className="text-blue-400 mt-1">•</span>
                <span>Include any symbolic elements you want to incorporate</span>
              </li>
              <li className="flex items-start gap-2">
                <span className="text-blue-400 mt-1">•</span>
                <span>Use the generated prompts in Midjourney, DALL-E, or Stable Diffusion</span>
              </li>
            </ul>
          </div>
        )}

        {/* What is Logo Prompt Section */}
        <div className="max-w-7xl mx-auto px-4 py-20 sm:px-6 lg:px-8">
          {/* Header Section */}
          <div className="text-center max-w-4xl mx-auto mb-20">
            <h1 className="text-5xl md:text-6xl lg:text-7xl font-bold text-white mb-8">
              What is <span className="text-purple-400 inline-block relative">
                Logo Prompt?
                <span className="absolute bottom-0 left-0 w-full h-2 bg-purple-500/20 rounded-full"></span>
              </span>
            </h1>
            <p className="text-xl md:text-2xl text-gray-400 leading-relaxed">
              Logo Prompt Generator transforms your brand information into professional logo design prompts optimized for AI tools like Midjourney, DALL-E, and Stable Diffusion. Create unique, brand-aligned logo concepts in seconds.
            </p>
          </div>

          {/* Features Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-32">
            <FeatureCard
              icon="🎨"
              title="AI-Optimized Prompts"
              description="Generate 5 unique logo prompts specifically crafted for AI image generators with perfect syntax and keywords."
            />
            <FeatureCard
              icon="🏢"
              title="Brand-Focused Design"
              description="Prompts are tailored to your industry, values, and target audience for authentic brand representation."
            />
            <FeatureCard
              icon="⚡"
              title="Instant Generation"
              description="Get professional logo prompts in seconds. No design experience needed - just describe your brand."
            />
            <FeatureCard
              icon="🎯"
              title="Multiple Variations"
              description="Receive 5 different prompt styles to explore various logo concepts and design directions."
            />
            <FeatureCard
              icon="🔧"
              title="Ready to Use"
              description="Copy and paste directly into Midjourney, DALL-E, or any AI image generator for immediate results."
            />
            <FeatureCard
              icon="💡"
              title="Creative Inspiration"
              description="Discover new design possibilities and creative directions you might not have considered."
            />
          </div>

          {/* FAQ Section */}
          <div className="max-w-5xl mx-auto mb-20">
            <div className="text-center mb-16">
              <h2 className="text-5xl md:text-6xl font-bold text-white mb-6">Frequently Asked Questions</h2>
              <p className="text-xl text-gray-400">Everything you need to know about Logo Prompt Generator</p>
            </div>

            <div className="space-y-6">
              {[
                {
                  question: "Which AI tools work with these prompts?",
                  answer: "Our prompts are optimized for Midjourney, DALL-E, Stable Diffusion, and most other AI image generators. They include proper syntax and keywords for best results."
                },
                {
                  question: "How detailed should my brand description be?",
                  answer: "The more specific you are, the better! Include your industry, target audience, brand values, and any symbolic elements you want. This helps generate more accurate and relevant prompts."
                },
                {
                  question: "Can I use these prompts for commercial purposes?",
                  answer: "Yes! The prompts are yours to use commercially. However, check the terms of your chosen AI image generator regarding commercial use of generated images."
                },
                {
                  question: "What if I don't like the generated prompts?",
                  answer: "Try refining your brand description with more specific details, different keywords, or alternative brand positioning. You can generate new prompts as many times as needed."
                },
                {
                  question: "Do I need design experience to use this?",
                  answer: "Not at all! Simply describe your brand and business, and we'll handle the technical prompt creation. The tool is designed for entrepreneurs and non-designers."
                },
                {
                  question: "How many logo variations will I get?",
                  answer: "Each generation provides 5 unique logo prompts with different styles and approaches, giving you multiple creative directions to explore."
                }
              ].map((faq, index) => (
                <FAQItem
                  key={index}
                  question={faq.question}
                  answer={faq.answer}
                  isOpen={openFAQ === index}
                  onClick={() => setOpenFAQ(openFAQ === index ? -1 : index)}
                />
              ))}
            </div>
          </div>

          {/* CTA Section */}
          <div className="max-w-4xl mx-auto">
            <div className="bg-gradient-to-r from-[#1a1a23] to-purple-950/50 border border-purple-500/20 rounded-3xl p-8 md:p-12 text-center relative overflow-hidden">
              {/* Background decoration */}
              <div className="absolute top-0 left-0 w-full h-full bg-[radial-gradient(circle_at_50%_0%,rgba(147,51,234,0.15),transparent_50%)]"></div>

              <div className="relative z-10">
                <h2 className="text-2xl md:text-3xl font-bold text-white mb-4">Need More Help?</h2>
                <p className="text-gray-400 mb-8 max-w-lg mx-auto">
                  Can't find the answer you're looking for? Our support team is ready to assist you with any questions about Logo Prompt Generator.
                </p>
                <button className="bg-gray-200 hover:bg-white text-gray-900 font-semibold py-3 px-8 rounded-lg transition-colors duration-200 inline-flex items-center gap-2">
                  <MessageCircle className="w-5 h-5" />
                  Contact Support
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LogoPrompt;
