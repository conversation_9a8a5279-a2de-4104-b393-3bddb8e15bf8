const express = require('express');
const router = express.Router();
const {
  getAllTools,
  getToolBySlug,
  incrementViews,
  incrementLoves
} = require('../controllers/tools.controller');

// GET /api/tools - Get all tools with stats
router.get('/', getAllTools);

// GET /api/tools/:slug - Get specific tool by slug
router.get('/:slug', getToolBySlug);

// POST /api/tools/:slug/view - Increment view count
router.post('/:slug/view', incrementViews);

// POST /api/tools/:slug/love - Increment love count
router.post('/:slug/love', incrementLoves);

module.exports = router;
