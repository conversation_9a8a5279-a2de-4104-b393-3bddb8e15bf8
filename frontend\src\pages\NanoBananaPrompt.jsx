import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Search, Heart, Copy, Check, X, Zap } from 'lucide-react';
import SEO from '../components/SEO';

// Mock data for prompts
const MOCK_PROMPTS = [
  {
    id: 1,
    title: "Cinematic Portrait",
    prompt: "Ultra-sharp, high-resolution portrait of a confident me(like on my photo attached) in a perfectly tailored black suit, black dress shirt, and black tie. I’m stands facing the camera with a subtle, intense expression — a blend of calm dominance and refined elegance. Do not change, edit, smooth, or retouch my face. Keep all skin texture, facial hair, and lighting exactly as in the original photo. The lighting is moody and cinematic: soft, directional studio lighting from the top front and side creates deep shadows and smooth highlights, perfectly sculpting the jawline, cheekbones, and suit details. The skin texture is natural and realistic — no retouching, no smoothing — every detail of the face is intact, showing authentic pores and stubble texture. The background is clean and minimalist, featuring a smooth dark gray-to-black gradient that adds subtle depth without distraction. Composition is vertical (9:16), with mid-thigh to head framing. The hands are slightly pulling on the suit jacket, adding a sense of movement and strength. The overall aesthetic is editorial fashion meets cinematic drama — bold, clean, masculine, and timeless. Style: Editorial, Cinematic, Ultra-Realistic Camera Settings: DSLR-quality, 85mm lens look, f/1.8 aperture for shallow depth of field, dramatic studio lighting Resolution: 8K Ultra HD Mood: Powerful, minimal, sharp, natural, confident",
    image: "https://ik.imagekit.io/q0mafimea/Prompt%20images/image-1.jpg?updatedAt=1765612290142",
    category: "Portrait",
    isFavorite: false
  },
  {
    id: 2,
    title: "Tracksuit Catalog",
        prompt: "Produce a hyper-realistic fashion catalog image in a bright white studio featuring a five-man lineup in a shallow arc, with a central foreground figure: a man wearing a mustard-yellow full-zip tracksuit with a stand collar, matching joggers, and white sneakers, plus sunglasses, serving as the focal point; flanked by four other models in coordinated tracksuits—teal, warm brown, and black variations—each with zip-front jackets and matching pants and white sneakers. The backdrop contains a large, translucent light-gray TRACKSUIT wordmark across the upper background, while at the very top a caption reads Premium Edition Tracksuits — Comfort • Style • Performance in a dark, sans-serif font. The lighting is bright and even, studio-quality with soft shadows, emphasizing crisp fabric textures and a premium athletic mood. Color palette features bold yet refined tones: mustard yellow, teal, brown, and black on a white set, with the text elements styled to resemble a catalog: the prominent TRACKSUIT watermark, the top caption in a bold sans-serif, and a small block of fine-print fabric information in the bottom-left corner.",

    image: "https://ik.imagekit.io/q0mafimea/Prompt%20images/image-2.jpeg?updatedAt=1765612289862",
    category: "Fashion",
    isFavorite: false
  },
  {
    id: 3,
    title: "Noir Portrait",
       prompt: "Create an ultra-realistic black-and-white portrait of a me ( using my selfie attached ) in dramatic low-key lighting.He is sitting slightly turned to the side, wearing a dark suit jacket and a white unbuttoned dress shirt. He has a well-groomed beard and short, styled hair.Pose:The man leans slightly back, looking directly into the camera with a confident, intense expression.His hands are brought together in front of him, fingers interlacing or nearly touching.A wristwatch with a dark face is visible on his right wrist.A subtle ring is visible on his finger.Lighting & Mood:Strong high-contrast low-key lighting from the left side.Deep shadows on the right side of the face and body for a dramatic noir effect.Background completely black, no visible details.Classic cinematic black-and-white toning.Sharp texture on skin, beard, and hands — zero smoothing.Camera Style:85mm or 50mm portrait lens.Editorial, luxury-brand aesthetic.High contrast, crisp detail, dramatic shadows.Overall Feel:Masculine, mysterious, elegant — reminiscent of a vintage noir film or luxury watch advertisement.",

    image: "https://ik.imagekit.io/q0mafimea/Prompt%20images/image-3.png?updatedAt=1765612291951",
    category: "Space",
    isFavorite: false
  },
  {
   id: 4,
    image: "https://ik.imagekit.io/q0mafimea/Prompt%20images/image-4.jpg?updatedAt=1765612289996",
    title: "Miyeon & Judy",
    prompt: "{ 'prompt': { 'characters': [ { 'name': 'Miyeon', 'description': 'beautiful young Korean woman, smiling, long black hair, wearing a white strapless top with black stars, silver necklace' }, { 'name': 'Judy Hopps', 'description': 'Disney character from Zootopia, wearing police uniform, smiling' } ], 'scene': { 'location': 'slightly dark, crowded movie theater/cinema hall', 'background': 'large movie screen showing a scene with multiple male characters in action poses', 'lighting': 'cinematic lighting' }, 'interaction': 'Miyeon taking a selfie with Judy Hopps, standing side-by-side', 'style': 'photorealistic, ultra-detailed, 8K' } }",
    category: "Character",
    isFavorite: false
  },
  {
   id: 5,
    image: "https://ik.imagekit.io/q0mafimea/Prompt%20images/image-5.jpg",
    title: "Aging Through Decades",
    prompt: "Using the uploaded photo as the identity reference, generate a realistic holiday portrait series of the SAME person through different ages. Create 6 portraits arranged in a clean 2x3 grid: Age 30s, 40s, 50s, 60s, 70s, and 80s. The person must clearly look like the same individual in every image: – Same facial structure – Same eye shape – Same nose and smile – Natural aging progression only. Style: Warm Christmas holiday atmosphere, cozy indoor living room Decorated Christmas tree with warm lights in the background Soft fireplace glow, festive bokeh lights Cinematic warm lighting, shallow depth of field High-end DSLR portrait photography Ultra-realistic skin texture, natural wrinkles with age No exaggeration, graceful aging. Clothing: Festive winter outfits (Christmas sweaters, knitwear, casual holiday jackets) Age-appropriate fashion for each decade. Camera & Quality: 85mm portrait lens look Sharp focus on face Soft background blur Photorealistic, high detail, professional studio quality. Composition: Smiling expression in all images Consistent framing and head position Clean background Text labels optional: “30s, 40s, 50s, 60s, 70s, 80s” Mood: Warm, joyful, nostalgic, festive, wholesome",
    category: "Portrait",
    isFavorite: false
  },
   {
     id: 6,
    image: "https://ik.imagekit.io/q0mafimea/Prompt%20images/image-6.png?updatedAt=1765612291068",
    title: "Figure & Packaging",
    prompt: "Create a 1/7 scale commercialized figure of the character in the photo. The style should be realistic, with clearly defined features, and placed in a real-world environment. The figure should be positioned on a computer desk, standing on a round transparent acrylic base with no text. On the computer screen, display the Adobe illustrator modelling process of this figure. Next to the screen, place a BANDAI-style toy packaging box printed with the original photo in a two-dimensional flat anime style.",
    category: "3D Design",
    isFavorite: false
  },
   {
    id: 7,
    image: "https://ik.imagekit.io/q0mafimea/Prompt%20images/image-7.jpg?updatedAt=1765612291710",
    title: "Dhaka Statue",
    prompt: "Create a giant hyper-realstic statue based on the given photo, keeping the original face exactly the same without change. The statue stands tall in the middle of a roundabout in Dhaka, near a historical landmark. The statue is still under construction, surrounded by scaffolding, with many construction workers in yellow helmets and orange vests climbing, welding, and working on it. Parts of statue's body are still exposed metal framework, while other sections are already detailed and finished. The background shows the realistic atmosphere in Dhaka city: crowded streets with colorful rickshaw, packed buses, and small cars circling the roundabout. Street vendors with tea stall, fruit carts, and colorful umbrellas line the roadside. Shop signs, big billboards, and messy hanging electric wires crisscross above the streets, creating the typical Dhaka city vibe. The bright daytime sky shines above, with topical trees and a bustling, lovely atmosphere. Style: photorealistic, vibrant, and full of life.",
    category: "Surreal",
    isFavorite: false
    
  },
  {
    id: 8,
    image: "https://ik.imagekit.io/q0mafimea/Prompt%20images/image-8.jpg?updatedAt=1765612291783",
    title: "Pen Sketch",
    prompt: "Generate a hand-drawn portrait illustration in red and yellow pen on notebook paper, inspired by doodle art and comic annotations. Keep full likeness of the subject, expressive lines, spontaneous gestures, bold outline glow, handwritten notes around, realistic pen stroke texture, 4K resolution.",
    category: "Illustration",
    isFavorite: false
    
  },
  {
    id: 9,
    image: "https://ik.imagekit.io/q0mafimea/Prompt%20images/image-9.jpg?updatedAt=1765612290817",
    title: "Pigeon Portrait",
    prompt: "8k Hyper-realistic, A cinematic portrait of a young 18 year old man, his hair is messy over the eye. I keep it real face from uploaded image in a long dark trench coat, surrounded by pigeons flying dramatically around the camera, wings blurred in motion, others perch on his shoulder. His expression is serious and enigmatic, half of his face obscured by a pigeon in the foreground, Moody lighting with strong shadows and highlights. Some pigeons fly very close to him.",
    category: "Cinematic",
    isFavorite: false
    
  },
  {
    id: 10,
    image: "https://ik.imagekit.io/q0mafimea/Prompt%20images/image-10.jpg?updatedAt=1765612291564",
    title: "Double Exposure",
    prompt: "cinematic double exposure digital artwork featuring a young man's side profile wearing sunglasses, seamlessly blended with a dramatic post-apocalyptic cityscape inside his silhouette. The inner silhouette shows the same man walking through a destroyed urban street with burning buildings, ruins, glowing embers, and fire all around. The lighting is moody and warm, with a fiery sunset casting golden highlights and deep shadows. The mood is heroic and introspective, emphasizing resilience and strength amidst chaos. High detail, realistic textures, 8K resolution, concept art style.",
    category: "Double Exposure",
    isFavorite: false
    
  },

  // --- Page 2 Content (IDs 11-20: New Data) ---
  {
    id: 11,
    image: "https://ik.imagekit.io/q0mafimea/Prompt%20images/image-11.jpg?updatedAt=1765624494191",
    title: "Rooftop Silhouette",
    prompt: "A digital illustration at sunset showing a South Asian teenage boy and a glowing silhouette of a girl standing face to face on a rooftop. The boy has medium-length dark hair, wearing a black t-shirt and pants, with a somber expression as he looks down slightly. The glowing female figure is softly touching his face with one hand, her form made of bright, warm white light. She has long flowing hair and feminine features that closely match the style and posture in the image. Her body contains bold black handwritten-style text: \"TERE NAAL MEIN MARANGI RANJHEYA VE.\" The setting sun casts an orange and purple glow over the rooftop and distant city buildings. A single streetlamp is visible behind them. The overall mood is emotional, cinematic, and surreal.",
    category: "Illustration",
    isFavorite: false
    
  },
  {
    id: 12,
    image: "https://ik.imagekit.io/q0mafimea/Prompt%20images/image-12.jpg?updatedAt=1765624494608",
    title: "Chibi Stickers",
    prompt: "Convert the image into a set of 12 chibi sticker (4x4 grid) with outfit similar to this one, including expression of laughing being angry, crying, sulking, thinking, being sleepy, blowing a kiss, winking, being surprise look ihw.",
    category: "Character",
    isFavorite: false
    
  },
  {
    id: 13,
    image: "https://ik.imagekit.io/q0mafimea/Prompt%20images/image-13.jpg?updatedAt=1765624494948",
    title: "3D Collage",
    prompt: "This hyper-realistic, studio-style photo collage features an adult Italian man (pictured) in various facial expressions and humorous poses. Each pose emerges from a torn piece of white paper, seemingly through a wall, with a 3D effect, as if emerging from the background. In each piece, the man wears a different outfit, ranging from casual and semi-formal to sporty, and even humorous or eccentric.Strong lighting and a simple light gray background. An elegant composition on a 3x3 grid, with each expression and body movement unique and interesting-for example, pointing, laughing, pretending to sleep, drinking, blinking, thinking, or gesturing.",
    category: "3D Design",
    isFavorite: false
   
  },
  {
    id: 14,
    image: "https://ik.imagekit.io/q0mafimea/Prompt%20images/image-14.jpg?updatedAt=1765624495335",
    title: "Graphic Portrait",
    prompt: "CREATE A HYPER-DETAILED GRAPHIC DESIGN FEATURING A STRIKING PORTRAIT OF A YOUNG MAN WITH THE SAME FACE AS UPLOADED] WITH A CONFIDENT DEMEANOUR. HIS HEAD IS ADORNED WITH VOLUMINOUS, ADDING TEXTURE AND DEPTH TO THE COMPOSITION. THE PORTRAIT IS RENDERED IN A HIGH-CONTRAST BLACK-AND-WHITE STYLE, STANDING OUT AGAINST THE MINIMALIST BACKGROUND. HIS EXPRESSION IS CALM YET DETERMINED. WITH ONE EYE PARTIALLY OBSCURED BY A BOLD RED RECTANGULAR OVERLAY THAT ADDS A MODERN, ARTISTIC FLAIR. THE BACKGROUND IS A SMOOTH, TEXTURED GREY CANVAS. SERVING AS A NEUTRAL BACKDROP THAT ENHANCES THE FOCAL ELEMENTS. OVERLAID VERTICALLY ALONG THE LEFT SIDE OF THE IMAGE, THE WORD \"PAUL SOMENDRA IS REPEATED IN LARGE. BOLD BLACK LETTERS WITH A SLIGHT TRANSPARENCY EFFECT. CREATINGA LAYERED, DYNAMIC LOOK INTERSPERSED WITHIN THIS TEXT ARE ICONIC DESIGN ELEMENTS: A PROMINENT NIKE LOGO IN RED NEAR THE TOP, A STYLIZED RED \"S\" LOWER DOWN. AND A VERTICAL RED LINE THAT PUNCTUATES THE DESIGN. TO THE RIGHT. A RED GEOMETRIC FRAME SURROUNDS THE OBSCURED EYE, DRAWING ATTENTION TO THE INTERPLAY OF COLOUR AND FORM. AT THE BOTTOM RIGHT. THE PHRASE \"WORK SMART NOT HARD\" IS WRITTEN IN BOLD RED CAPITAL LETTERS, WITH \"SMART\" IN A SMALLER, ELEGANT CURSIVE SCRIPT BENEATH IT SIGNED OFF WITH \"GRAPHICS\" IN A MATCHING STYLE, SUGGESTING A PERSONAL OR BRAND SIGNATURE. THE BOTTOM LEFT CORNER FEATURES THE HASHTAG #genfreeai\" IN RED, REINFORCING THE IDENTITY THEME. THE YOUNG MAN'S ATTIRE, A PARTIALLY VISIBLE BLACK LEATHER JACKET WITH AN OPEN COLLAR, ADDS A RUGGED YET STYLISH EDGE TO THE OVERALL AESTHETIC.THE LIGHTING IS SOFT YET DRAMATIC. HIGHLIGHTING THE TEXTURES OF HIS HAIR AND JACKET, WHILE THE RED ACCENTS POP VIVIDLY AGAINST THE GRAYSCALE TONES, CREATING A COHESIVE, HIGH-ENERGY VISUAL THAT BLENDS STREETWEAR CULTURE WITH GRAPHIC ARTISTRY. PHOTOREALISTIC, SHALLOW DEPTH OF FIELD, HIGH-RESOLUTION DSLR QUALITY, HASSELBLAD X2D 100C, SHALLOW DEPTH OF FIELD, SHARPLY FOCUSED ON ME. 4:5 ASPECT RATIO. MAKE IT 8K ULTRA REALISTIC, HYPER DETAILED",
    category: "Illustration",
    isFavorite: false
    
  },
  {
    id: 15,
    image: "https://ik.imagekit.io/q0mafimea/Prompt%20images/image-15.jpg?updatedAt=1765624495019",
    title: "Rooftop Doves",
    prompt: "8K cinematic portrait of a stylish young man on a rooftop at sunset. Natural messy hair, loose olive leather jacket, black t-shirt with Avanger logo, and reflective aviators. He gently holds a white dove while more doves fly around him. Warm golden light highlights his face and feathers. Soft blurred city skyline, dreamy emotional mood. Hyper-realistic, highly detailed textures, soft shadows, dramatic yet peaceful atmosphere.",
    category: "Cinematic",
    isFavorite: false
    
  },
  {
    id: 16,
    image: "https://ik.imagekit.io/q0mafimea/Prompt%20images/image-16.jpg?updatedAt=1765624495110",
    title: "Bioluminescent Beach",
    prompt: "Give the exact same face 8k ultra image. young man walking bare feet along a glowing bioluminescent beach at night, blue sparkles lighting up with each step. vertical pendant thin silver chain, smartwatch visible, other hand holds a bunch of red roses. Wearing a darkred lenin shirt half-tucked with black trousers. Slow walking with cool attitude pose, off camera. DSLR 35mm wide-angle, cool neon Lightroom edit, deep shadows with glowing surf. Use uploaded face.wear black sunglasses",
    category: "Surreal",
    isFavorite: false
    
  },
  {
    id: 17,
    image: "https://ik.imagekit.io/q0mafimea/Prompt%20images/image-17.jpg?updatedAt=1765624494875",
    title: "Neon Forest",
    prompt: "A dark fantasy painting depicting an enchanted forest where luminescent neon blue and turquoise daisies glow like mystical beacons scattered across the woodland floor. The ethereal flowers emit a soft, supernatural radiance that contrasts sharply with the twisted, gnarled tree trunks and shadowy undergrowth surrounding them. Dense fog weaves between the ancient trees, creating mysterious silhouettes and depth, while shafts of pale moonlight filter through the dark canopy above. The overall atmosphere is both haunting and mesmerizing, with the glowing daisies serving as points of otherworldly beauty in this eerie, enchanted realm.",
    category: "Nature",
    isFavorite: false
    
  },
  {
    id: 18,
    image: "https://ik.imagekit.io/q0mafimea/Prompt%20images/image-18.jpg?updatedAt=1765624494610",
    title: "Card Telekinesis",
    prompt: "A young man with dark hair(maintain exact face and skin tone with sharp features) wearing a black hoodie sits in a rustic wooden booth, staring intensely at the camera with one hand supporting his chin and the other reaching forward as if performing telekinesis. Dozens of playing cards fly around him in mid-air, captured n dynamic slow-motion with motion blur. The background shows vertical wooden panels and a framed photo of a man playing cards.",
    category: "Magic",
    isFavorite: false
    
  },
  {
    id: 19,
    image: "https://ik.imagekit.io/q0mafimea/Prompt%20images/image-19.jpg?updatedAt=1765624494469",
    title: "Mountain Portrait",
    prompt: "Create a hyper-realistic outdoor portrait using my selfie. Keep the face exactly as it appears in the original photo — no editing, no smoothing, no facial changes. The subject is standing against a stunning mountain backdrop, surrounded by lush greenery and golden autumn foliage. The weather is crisp and clear, with soft, warm sunlight filtering through the trees. Add distant rolling hills, colorful leaves, and natural depth in the background. Use shallow depth of field to keep the focus on the subject while subtly blurring the scenic landscape behind. The overall tone should feel cinematic, natural, and editorial-quality — like a high-end travel magazine cover. Vertical 9:16 format, 4K resolution, with ultra-realistic textures and lighting.",
    category: "Portrait",
    isFavorite: false
    
  },
  {
    id: 20,
    image: "https://ik.imagekit.io/q0mafimea/Prompt%20images/image-20.jpg?updatedAt=1765624495127",
    title: "Squid Game Poster",
    prompt: "A dramatic promotional poster for a dark survival drama series titled ‘Squid Game Season 4’. The poster features a group of diverse characters in green tracksuits with unique numbers on their chests, standing in a surreal, colorful, maze-like room with staircases and doors on the walls. The lead character (face photo attached) stands in the center, wearing a tracksuit labeled ‘456’, looking serious and determined. The background is a night sky with stars painted on the walls. Above them, the text reads: ‘TIME TO PLAY ONE LAST TIME’. Below, the title ‘SQUID GAME 4’ is prominently displayed with the Netflix logo and the release date ‘27 JUNE’. The atmosphere is tense and mysterious, with dramatic lighting and shadows emphasizing the high stakes of the game.” 8k resolution, Image Size Ratio 2:3",
    category: "Cinematic",
    isFavorite: false
    
  },

  // --- Page 3 Content (IDs 21-30) ---
  {
    id: 21,
    image: "https://ik.imagekit.io/q0mafimea/Prompt%20images/image-21.jpg",
    title: "Vintage Watercolor",
    prompt: "Create an elegant vintage watercolor portrait using the uploaded human photo as the exact facial reference. Preserve the person’s identity, facial structure, skin tone, and expression accurately. Style: Soft watercolor painting with a romantic, antique aesthetic. Muted warm color palette (beige, cream, sepia, dusty rose, soft brown). Delicate hand-painted textures, visible watercolor washes, and paper grain. Composition: Half-body or bust portrait, centered. Subject wearing vintage-inspired elegant clothing (lace dress, soft fabric, classic 19th-century / romantic style). Natural graceful pose, calm expression, soft lighting. Background: Artistic collage background with: - Antique handwritten letters and old paper textures - Vintage keys, floral illustrations - Roses, dried flowers, botanical elements - Ink splashes, watercolor stains, subtle paint drips Background should blend smoothly into the portrait. Details: High-detail facial features, soft realistic eyes, natural skin texture. Painterly edges, slightly dreamy and ethereal look. No modern objects, no digital sharpness. Looks like a hand-painted fine art illustration. Quality: Ultra high resolution, professional artwork, museum-quality illustration.",
    category: "Illustration",
    isFavorite: false
    
  },
  {
    id: 22,
    image: "https://ik.imagekit.io/q0mafimea/Prompt%20images/image-22.jpg",
    title: "Money Rain Studio",
    prompt: "Use the uploaded person as the main subject. Create a cinematic studio portrait with the same face, body proportions, and identity preserved. The person is standing confidently in the center, full body visible, feet on the ground, arms relaxed slightly open. Money is raining from above — realistic US dollar bills floating and falling in mid-air, some suspended, some scattered on the floor. Dark studio background with dramatic lighting: • soft warm light from the left • cool rim light from the right • high contrast, cinematic shadows • professional fashion photoshoot look Outfit style: • casual streetwear • oversized black hoodie • slim blue jeans • clean white sneakers Ultra-realistic photography, sharp focus, 85mm lens look, shallow depth of field, high detail skin texture, natural facial expression, confident attitude. Luxury, success, power vibe. 8K quality, photorealistic, studio lighting, editorial fashion photography.",
    category: "Fashion",
    isFavorite: false
    
  },
  {
    id: 23,
    image: "https://ik.imagekit.io/q0mafimea/Prompt%20images/image-23.jpg",
    title: "Corporate Art Fusion",
    prompt: "Transform the uploaded person into a high-end cinematic corporate portrait. The subject is standing confidently behind a modern wooden desk, hands resting on the table, strong leadership posture. They are wearing an elegant dark navy business suit with a light inner shirt, professional executive appearance. Behind the person is a large abstract painted portrait of the same person, created with bold expressive brush strokes. The painting uses deep navy blue, charcoal black, beige, and soft cream tones. Painterly, modern fine-art style, semi-abstract, visible brush textures, gallery-quality canvas artwork. The environment is a minimalist modern office with concrete walls, large floor-to-ceiling windows, soft natural daylight, clean architectural lines. Cinematic lighting, soft shadows, shallow depth of field, ultra-realistic face details. The face must strongly resemble the uploaded person. Professional, powerful, confident mood. Editorial photography + contemporary art fusion. Ultra-high resolution, sharp focus, photorealistic subject, artistic background.",
    category: "Portrait",
    isFavorite: false
    
  },
  {
    id: 24,
    image: "https://ik.imagekit.io/q0mafimea/Prompt%20images/image-24.jpg",
    title: "Neon Halo Portrait",
    prompt: "Create a high-quality studio portrait using the uploaded person as the main subject. Keep the face identity, skin tone, and facial structure accurate. Style: A modern, clean cut-out portrait with a soft glowing white outline around the subject (neon halo effect). The subject is centered, sharp focus, professional lighting, smooth skin, cinematic look. Background: Minimal flat gray background with subtle floating doodle-style icons and objects around the subject. Icons include hand-drawn stars, dice, music headphones, guitar, graffiti-style text, and cute cartoon symbols. Icons are monochrome, simple line-art, slightly blurred, and evenly spaced. Lighting & Color: Soft diffused studio lighting, no harsh shadows. Natural skin tones, slightly warm. High contrast between subject and background. Polished social-media / influencer aesthetic. Composition: Medium shot (waist-up or thigh-up). Subject slightly angled, confident relaxed pose. Clean edges, perfect cutout, glowing rim light. Ultra-detailed, photorealistic, 4K quality, professional editorial photography.",
    category: "Character",
    isFavorite: false
    
  },
  {
    id: 25,
    image: "https://ik.imagekit.io/q0mafimea/Prompt%20images/image-25.jpg",
    title: "Charcoal Oil Mix",
    prompt: "Create an artistic portrait using the uploaded person as the reference. Preserve the person’s facial identity, face shape, eyes, nose, lips, and skin tone accurately. Art Style: Elegant hand-painted portrait mixed with charcoal sketch and soft oil painting. Loose expressive pencil strokes around the edges. Painterly brush textures with visible sketch lines. Soft blended shading, slightly dramatic, fine-art illustration look. Face & Details: Highly detailed face, realistic eyes with soft highlights. Smooth painterly skin with natural texture. Subtle blush, gentle highlights on cheekbones and nose. Hair painted with loose flowing strokes, slightly messy and natural. Composition: 3/4 view portrait, shoulder-up or bust portrait. Subject slightly turned, calm and confident expression. Off-shoulder or elegant clothing suggested with brush strokes, not sharp details. Background: Minimal abstract background with rough charcoal strokes and soft pastel washes. Muted neutral tones (beige, gray, soft green). Background stays painterly and unfinished, focus on the face. Lighting & Mood: Soft studio lighting, painterly highlights. Warm cinematic tones, fine-art gallery quality. Ultra-detailed, artistic, high-resolution, professional digital painting.",
    category: "Illustration",
    isFavorite: false
    
  },
  {
    id: 26,
    image: "https://ik.imagekit.io/q0mafimea/Prompt%20images/image-26.jpg",
    title: "Neon Doodle Aura",
    prompt: "Create a high-quality portrait using the uploaded person as the main subject. Preserve the person’s facial identity, face shape, skin tone, and expression accurately. Style: Photorealistic portrait combined with glowing neon doodle illustrations. Hand-drawn neon light trails, music notes, stars, planets, waves, and abstract symbols flowing in a circular motion around the subject’s head, forming an energy aura. Neon lines look like light painting, vibrant pink and white glow. Subject Details: Side-profile or 3/4 profile portrait. Eyes closed or calm expression, peaceful mood. Wearing headphones (or add modern headphones if not present). Natural skin texture, soft cinematic lighting. Background: Smooth gradient background (cyan to blue). Clean, minimal, no clutter. Strong contrast between subject and neon elements. Lighting & Mood: Soft studio lighting on face. Neon elements emit glow light that subtly reflects on the subject. Dreamy, immersive, music-inspired atmosphere. Composition: Medium shot (chest-up). Neon doodles wrap around the head without covering the face. Sharp subject, glowing effects crisp and clean. Ultra-detailed, photorealistic, cinematic, 4K quality, modern digital art.",
    category: "Surreal",
    isFavorite: false
    
  },
  {
    id: 27,
    image: "https://ik.imagekit.io/q0mafimea/Prompt%20images/image-27.jpg",
    title: "Comic Vector Style",
    prompt: "Create a stylized illustration using the uploaded person as the main reference. Preserve the person’s facial identity, face shape hairstyle, and expression. Art Style: Bold comic-style vector illustration. Clean sharp outlines, smooth flat shading, high contrast. Graphic poster look with dynamic hand-drawn energy lines and lightning shapes. Modern digital illustration, not photorealistic. Subject: Side-profile or 3/4 view portrait. Eyes closed, enjoying music, relaxed confident expression. Wearing modern streetwear (jacket, cap, earbuds or headphones). Strong silhouette and clear facial structure. Effects: White and black hand-drawn lightning strokes around the head and body, representing sound waves and bass impact. Dynamic motion lines, energetic composition. Text Elements: Add stylized handwritten text near the subject: “BASS MODE”, “SOUND RUSH”, “FEEL IT” Text looks like comic lettering, bold, playful, integrated into the design. Background: Solid vibrant orange background. Flat color, no texture, high contrast with subject. Lighting & Mood: High-energy, music-poster vibe. Bold shadows, clean highlights. Street-art + comic illustration aesthetic. Ultra-clean vector look, high resolution, professional poster quality.",
    category: "Illustration",
    isFavorite: false
    
  },
  {
    id: 28,
    image: "https://ik.imagekit.io/q0mafimea/Prompt%20images/image-28.jpg",
    title: "Realistic Pencil Sketch",
    prompt: "Transform the uploaded human photo into an elegant realistic pencil sketch portrait. Style: hand-drawn graphite illustration, fine pencil lines, soft shading, smooth gradients, high detail facial features. The subject should look natural and realistic, with accurate facial proportions preserved from the original photo. Use delicate cross-hatching and light sketch strokes, subtle shadows, soft highlights, and clean contours. Clothing rendered in minimalistic line art, flowing fabric folds, soft texture. Background: plain white or very light textured paper, no distractions. Mood: calm, thoughtful, artistic, slightly dreamy expression. Color: black and white / grayscale only, no colors. Quality: high-resolution, professional art illustration, realistic pencil drawing, museum-quality sketch.",
    category: "Illustration",
    isFavorite: false
    
  },
  {
    id: 29,
    image: "https://ik.imagekit.io/q0mafimea/Prompt%20images/image-29.jpg",
    title: "Flat Vector Portrait",
    prompt: "Convert the uploaded human photo into a modern flat vector illustration portrait. Style: clean geometric shapes, smooth edges, flat colors with soft gradient shading, minimal but elegant details. Preserve the person’s facial structure, expression, hairstyle, glasses (if present), and pose accurately from the original photo. Skin tones should be smooth and stylized, not photorealistic. Hair illustrated with layered vector shapes and subtle highlights. Clothing rendered in bold solid colors with simple folds and shadows. Add a minimal abstract background using diagonal or geometric shapes, complementary muted colors, and lots of white space. Look & feel: professional digital illustration, Dribbble / Behance style, clean, modern, stylish. No texture, no brush strokes, no realism — pure vector art look. High resolution, sharp lines, perfectly balanced composition.",
    category: "Illustration",
    isFavorite: false
    
  },
  {
    id: 30,
    image: "https://ik.imagekit.io/q0mafimea/Prompt%20images/image-30.jpg",
    title: "Safari Golden Hour",
    prompt: "Create a cinematic, ultra-realistic wildlife portrait using the uploaded human image as the main subject. Place the person naturally in an African savanna during golden hour sunset, tall dry grass, distant acacia trees, warm amber and orange sky. The subject is calmly interacting with a majestic wild leopard beside them, showing trust and harmony. Soft glowing particles floating in the air, dreamy atmosphere, shallow depth of field, cinematic lighting, soft rim light around the subject, realistic skin texture, natural facial expression. Color grading: warm golden tones, soft contrast, cinematic teal-orange look. Shot on a professional DSLR, 85mm lens, f/1.8, ultra-high detail, photorealistic, 8K, National Geographic style, documentary photography, emotionally powerful, realistic proportions.",
    category: "Nature",
    isFavorite: false
    
  }
];

const CATEGORIES = [ "ALL", 
  
  "Portrait", 
  "Fashion", 
  "Cinematic", 
  "Illustration", 
  "Double Exposure", 
  "Character", 
  "Surreal", 
  "3D Design", 
  "Sci-Fi", 
  "Nature",
  "Vintage",
  "Magic",
  "Abstract"];

export default function NanoBananaPrompt() {
  const { page } = useParams();
  const navigate = useNavigate();

  const [prompts, setPrompts] = useState(() => {
    if (typeof window !== 'undefined') {
      const saved = localStorage.getItem('nano_banana_prompts_v4');
      if (saved) {
        try {
          const savedPrompts = JSON.parse(saved);
          return MOCK_PROMPTS.map(mockItem => {
            const savedItem = savedPrompts.find(p => p.id === mockItem.id);
            return savedItem ? { ...mockItem, isFavorite: savedItem.isFavorite } : mockItem;
          });
        } catch (e) {
          return MOCK_PROMPTS;
        }
      }
    }
    return MOCK_PROMPTS;
  });

  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('ALL');
  const [showFavoritesOnly, setShowFavoritesOnly] = useState(false);
  const [selectedImage, setSelectedImage] = useState(null);
  const [currentPage, setCurrentPage] = useState(() => {
    // Initialize current page from URL parameter
    const pageNum = parseInt(page) || 1;
    return pageNum > 0 ? pageNum : 1;
  });
  const [copiedId, setCopiedId] = useState(null);
  const itemsPerPage = 10;

  // Save to localStorage whenever prompts change
  useEffect(() => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('nano_banana_prompts_v4', JSON.stringify(prompts));
    }
  }, [prompts]);

  // Update current page when URL parameter changes
  useEffect(() => {
    const pageNum = parseInt(page) || 1;
    const validPageNum = pageNum > 0 ? pageNum : 1;
    setCurrentPage(validPageNum);
  }, [page]);

  // Disable/enable body scroll when modal is open/closed
  useEffect(() => {
    if (selectedImage) {
      // Disable scrolling
      document.body.style.overflow = 'hidden';
    } else {
      // Re-enable scrolling
      document.body.style.overflow = 'unset';
    }

    // Cleanup function to ensure scrolling is re-enabled when component unmounts
    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [selectedImage]);

  // Filter prompts based on search, category, and favorites
  const filteredPrompts = prompts.filter(prompt => {
    const matchesSearch = prompt.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         prompt.prompt.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === 'ALL' || prompt.category === selectedCategory;
    const matchesFavorites = !showFavoritesOnly || prompt.isFavorite;

    return matchesSearch && matchesCategory && matchesFavorites;
  });

  // Pagination
  const totalPages = Math.ceil(filteredPrompts.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const currentPrompts = filteredPrompts.slice(startIndex, startIndex + itemsPerPage);

  // Reset to first page when filters change
  useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm, selectedCategory, showFavoritesOnly]);

  const handleCopy = (text) => {
    navigator.clipboard.writeText(text);
    setCopiedId(text);
    setTimeout(() => setCopiedId(null), 2000);
  };

  const toggleFavorite = (id) => {
    setPrompts(prev => prev.map(prompt => 
      prompt.id === id ? { ...prompt, isFavorite: !prompt.isFavorite } : prompt
    ));
  };

  const handlePageChange = (pageNum) => {
    setCurrentPage(pageNum);

    // Update URL based on page number
    if (pageNum === 1) {
      // For page 1, navigate to base URL without page parameter
      navigate('/nano-banana-prompt', { replace: true });
    } else {
      // For other pages, include page parameter in URL
      navigate(`/nano-banana-prompt/page/${pageNum}`, { replace: true });
    }

    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  return (
    <>
      <SEO 
        title="Nano Banana Prompt Gallery - AI Art Prompts Collection"
        description="Discover and explore our curated collection of AI art prompts. Find inspiration for your next creative project with our searchable prompt gallery."
        keywords="AI prompts, art prompts, creative prompts, AI art, prompt gallery, nano banana"
      />
      
      <div className="min-h-screen  text-white">
        {/* Header */}
        <div className="">
          <div className="max-w-7xl mx-auto px-4 py-12">
            <div className="text-center">
              <div className="flex items-center justify-center gap-3 mb-4">
                <Zap className="w-8 h-8 text-yellow-500" />
                <h1 className="text-4xl md:text-6xl font-bold text-white bg-clip-text text-transparent">
                  Nano Banana <span className="text-yellow-500">Prompt Gallery</span>

                </h1>
              </div>
              <p className="text-xl text-gray-300 max-w-2xl mx-auto">
                Discover amazing AI art prompts to fuel your creativity
              </p>
            </div>
          </div>
        </div>

        {/* Search and Filter Section */}
        <div className="max-w-7xl mx-auto px-4 py-8">
          {/* Search Bar */}
          <div className="flex justify-center mb-8">
            <div className="relative w-full max-w-2xl">
              <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <input
                type="text"
                placeholder="Search for image prompts..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-12 pr-6 py-4 bg-gray-800/50 border border-gray-600 rounded-full text-white placeholder-gray-400 focus:outline-none focus:border-gray-500 focus:bg-gray-800/70 transition-all duration-200 text-lg"
              />
            </div>
          </div>

          {/* Category Filter Pills and Favorites */}
          <div className="flex flex-wrap justify-center gap-3 mb-8">
            {CATEGORIES.map((category) => (
              <button
                key={category}
                onClick={() => setSelectedCategory(category)}
                className={`px-6 py-2.5 rounded-full text-sm font-medium transition-all duration-200 ${
                  selectedCategory === category
                    ? 'bg-[#F0B100] text-black shadow-lg shadow-blue-600/25 '
                    : 'bg-gray-700/50 text-gray-300 hover:bg-gray-600/50 hover:text-white border border-gray-600'
                }`}
              >
                {category}
              </button>
            ))}

            {/* Favorites Button - positioned after ALL */}
            <button
              onClick={() => setShowFavoritesOnly(!showFavoritesOnly)}
              className={`flex items-center gap-2 px-6 py-2.5 rounded-full text-sm font-medium transition-all duration-200 ${
                showFavoritesOnly
                  ? 'bg-yellow-600 text-white shadow-lg shadow-yellow-600/25'
                  : 'bg-gray-700/50 text-gray-300 hover:bg-gray-600/50 hover:text-white border border-gray-600'
              }`}
            >
              <Heart size={16} fill={showFavoritesOnly ? "currentColor" : "none"} />
              Favorites
            </button>
          </div>

          {/* Active Filters Tags and Results Info */}
          <div className="flex flex-wrap justify-center items-center gap-4 mb-6">
            {/* Filter Tags */}
            {(searchTerm || selectedCategory !== 'All' || showFavoritesOnly) && (
              <>
                {/* Search Term Tag */}
                {searchTerm && (
                  <div className="flex items-center gap-2 px-3 py-1.5 bg-gray-800 text-white text-sm rounded-full border border-gray-600">
                    <span>Search: "{searchTerm}"</span>
                    <button
                      onClick={() => setSearchTerm('')}
                      className="hover:bg-gray-700 rounded-full p-0.5 transition-colors"
                    >
                      <X className="w-3 h-3" />
                    </button>
                  </div>
                )}

                {/* Category Tag */}
                {selectedCategory !== 'ALL' && (
                  <div className="flex items-center gap-2 px-3 py-1.5 bg-gray-800 text-white text-sm rounded-full border border-gray-600">
                    <span>{selectedCategory}</span>
                    <button
                      onClick={() => setSelectedCategory('ALL')}
                      className="hover:bg-gray-700 rounded-full p-0.5 transition-colors"
                    >
                      <X className="w-3 h-3" />
                    </button>
                  </div>
                )}

                {/* Favorites Tag */}
                {showFavoritesOnly && (
                  <div className="flex items-center gap-2 px-3 py-1.5 bg-gray-800 text-white text-sm rounded-full border border-gray-600">
                    <Heart className="w-3 h-3" fill="currentColor" />
                    <span>Favorites</span>
                    <button
                      onClick={() => setShowFavoritesOnly(false)}
                      className="hover:bg-gray-700 rounded-full p-0.5 transition-colors"
                    >
                      <X className="w-3 h-3" />
                    </button>
                  </div>
                )}
              </>
            )}

            {/* Results Info */}
            <p className="text-gray-400 text-sm">
              {filteredPrompts.length} prompt{filteredPrompts.length !== 1 ? 's' : ''} found
            </p>
          </div>

          {/* Prompts Grid */}
          {currentPrompts.length > 0 ? (
            <div className="columns-1 md:columns-2 lg:columns-2 xl:columns-3 gap-8 space-y-8">
              {currentPrompts.map((prompt) => (
                <PromptCard
                  key={prompt.id}
                  item={prompt}
                  onCopy={handleCopy}
                  onToggleFavorite={toggleFavorite}
                  onImageClick={setSelectedImage}
                  isCopied={copiedId === prompt.prompt}
                />
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <p className="text-gray-400 text-lg">No prompts found matching your criteria.</p>
            </div>
          )}

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="flex flex-col items-center justify-center gap-3 sm:gap-4 mt-12 px-4">
              <div className="flex items-center gap-1 sm:gap-2 overflow-x-auto max-w-full">
                <button
                  onClick={() => handlePageChange(currentPage - 1)}
                  disabled={currentPage === 1}
                  className="px-2 sm:px-4 h-8 sm:h-10 flex items-center justify-center rounded-sm bg-[#1c2128] border border-gray-800 text-gray-400 hover:text-white hover:border-gray-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed text-xs sm:text-sm font-medium flex-shrink-0"
                >
                  <span className="hidden sm:inline">Previous</span>
                  <span className="sm:hidden">Prev</span>
                </button>

                <div className="flex items-center gap-1 sm:gap-2 overflow-x-auto">
                  {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                    <button
                      key={page}
                      onClick={() => handlePageChange(page)}
                      className={`w-8 sm:w-12 h-8 sm:h-10 flex items-center justify-center rounded-lg transition-colors font-medium text-sm sm:text-xl flex-shrink-0 ${
                        currentPage === page
                          ? "bg-yellow-500 text-black font-bold shadow-[0_0_10px_rgba(234,179,8,0.4)]"
                          : "bg-[#1c2128] border border-gray-800 text-gray-400 hover:text-white hover:border-gray-600"
                      }`}
                    >
                      {page}
                    </button>
                  ))}
                </div>

                <button
                  onClick={() => handlePageChange(currentPage + 1)}
                  disabled={currentPage === totalPages}
                  className="px-2 sm:px-4 h-8 sm:h-10 flex items-center justify-center rounded-lg bg-[#1c2128] border border-gray-800 text-gray-400 hover:text-white hover:border-gray-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed text-xs sm:text-sm font-medium flex-shrink-0"
                >
                  <span className="hidden sm:inline">Next</span>
                  <span className="sm:hidden">Next</span>
                </button>
              </div>

              <div className="text-gray-500 text-xs sm:text-sm font-medium text-center">
                Page {currentPage} of {totalPages}
              </div>
            </div>
          )}
        </div>

        {/* Image Preview Modal */}
        {selectedImage && (
          <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4">
            <div className="relative max-w-4xl max-h-full">
              <button
                onClick={() => setSelectedImage(null)}
                className="absolute top-4 right-4 z-10 p-2 bg-black/50 rounded-full text-white hover:text-gray-300 hover:bg-black/70 transition-all duration-200"
              >
                <X className="w-6 h-6" />
              </button>
              <img
                src={selectedImage}
                alt="Preview"
                className="max-w-full max-h-full object-contain rounded-lg"
              />
            </div>
          </div>
        )}
      </div>
    </>
  );
}

// PromptCard Component
function PromptCard({ item, onCopy, onToggleFavorite, onImageClick, isCopied }) {
  const [isHovered, setIsHovered] = useState(false);
  const [imageLoaded, setImageLoaded] = useState(false);

  const handleCopyClick = () => {
    onCopy(item.prompt);
  };

  return (
    <div
      className="break-inside-avoid bg-[#161b22] rounded-xl overflow-hidden border border-gray-800 hover:border-yellow-500/50 transition-all duration-300 shadow-lg hover:shadow-yellow-500/10 group mb-6"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Image Area */}
      <div
        className="relative bg-gray-800 cursor-pointer min-h-[250px]"
        onClick={() => onImageClick(item.image)}
      >
        {/* Skeleton Loader */}
        {!imageLoaded && (
          <div className="absolute inset-0 z-10 bg-gray-800 animate-pulse flex flex-col items-center justify-center text-gray-600">
            <div className="w-12 h-12 mb-2 rounded-full border-4 border-gray-700 border-t-yellow-500 animate-spin"></div>
            <span className="text-xs font-medium">Loading...</span>
          </div>
        )}

        <img
          src={item.image}
          alt={item.title}
          className={`w-full h-auto block transform group-hover:scale-105 transition-all duration-500 ${imageLoaded ? 'opacity-100' : 'opacity-0'}`}
          loading="lazy"
          onLoad={() => setImageLoaded(true)}
        />

        <div className="absolute top-2 right-2 flex gap-2 opacity-0 group-hover:opacity-100 transition-opacity z-20">
          <button
            onClick={(e) => {
              e.stopPropagation();
              onToggleFavorite(item.id);
            }}
            className={`p-2 rounded-full backdrop-blur-sm transition-all duration-200 transform active:scale-90 ${
              item.isFavorite
                ? "bg-red-500 text-white shadow-lg shadow-red-500/40"
                : "bg-black/60 text-white hover:bg-red-500 hover:text-white"
            }`}
          >
            <Heart size={16} fill={item.isFavorite ? "currentColor" : "none"} />
          </button>
        </div>
      </div>

      {/* Content Area */}
      <div className="p-4">
        <h3 className="text-xl font-bold text-white mb-2">{item.title}</h3>

        {/* Prompt Text */}
        <div className="bg-[#0d1117] rounded-lg p-3 mb-4 font-mono text-xs text-gray-400 border border-gray-800/50 h-32 overflow-y-auto">
          {item.prompt}
        </div>

        {/* Actions */}
        <div>
          <button
            onClick={handleCopyClick}
            className={`w-full flex items-center justify-center gap-2 text-sm font-semibold py-2.5 rounded-lg transition-all duration-200 shadow-lg ${
              isCopied
                ? "bg-green-500 hover:bg-green-400 text-white shadow-green-500/20"
                : "bg-yellow-500 hover:bg-yellow-400 text-black shadow-yellow-500/20"
            }`}
          >
            {isCopied ? <Check size={16} /> : <Copy size={16} />}
            {isCopied ? "Copied" : "Copy Prompt"}
          </button>
        </div>
      </div>
    </div>
  );
}
