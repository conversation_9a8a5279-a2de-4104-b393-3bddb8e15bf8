const aiservice = require("../services/ai.service");
const Image = require("../../models/Image");
const Replicate = require("replicate");

const getResult = async (req, res) => {
try {
    const { prompt, aspectRatio } = req.body;
    const imageUrl = await aiservice(prompt, aspectRatio);

    // Save image to MongoDB if generation was successful
    if (imageUrl) {
        // Save to database
        try {
            const newImage = new Image({
                prompt: prompt,
                imageUrl: imageUrl,
                aspectRatio: aspectRatio,
                userAgent: req.get('User-Agent') || '',
                ipAddress: req.ip || req.connection.remoteAddress || ''
            });

            await newImage.save();
        } catch (dbError) {
            console.error('❌ Failed to save image to database:', dbError.message);
            // Don't fail the request if DB save fails
        }
    }

    // Return the result in the expected format
    return res.json({ imageUrl: imageUrl });
} catch (error) {
    console.error('❌ Error in getResult:', error);
    return res.status(500).json({ error: 'Failed to generate image' });
}
};

const removeBackground = async (req, res) => {
    try {
        const { imageData } = req.body;

        if (!imageData) {
            return res.status(400).json({ error: 'Image data is required' });
        }

        // Initialize Replicate
        const replicate = new Replicate({
            auth: process.env.AI_KEY,
        });

        const output = await replicate.run(
            "codeplugtech/background_remover:37ff2aa89897c0de4a140a3d50969dc62b663ea467e1e2bde18008e3d3731b2b",
            {
                input: {
                    image: imageData
                    
                }
            }
        );

        // Handle different output formats from Replicate
        let processedImageUrl;

        if (typeof output === 'string') {
            // Direct URL string
            processedImageUrl = output;
        } else if (Array.isArray(output) && output.length > 0) {
            // Array of URLs
            processedImageUrl = output[0];
        } else if (output && typeof output.url === 'function') {
            // File object with url() method
            processedImageUrl = output.url();
        } else if (output && output.url) {
            // Object with url property
            processedImageUrl = output.url;
        } else {
            // For streams or other formats, we need to convert to base64 or handle differently
            processedImageUrl = output;
        }

        // Return the processed image URL
        return res.json({
            success: true,
            processedImageUrl: processedImageUrl,
            message: 'Background removed successfully'
        });

    } catch (error) {
        console.error('❌ Error in removeBackground:', error);
        return res.status(500).json({
            error: 'Failed to remove background',
            details: error.message
        });
    }
};

const upscaleImage = async (req, res) => {
    try {
        const { imageData, scale = 2, faceEnhance = false } = req.body;

        if (!imageData) {
            return res.status(400).json({ error: 'Image data is required' });
        }

        // Initialize Replicate
        const replicate = new Replicate({
            auth: process.env.AI_KEY,
        });

        // Call Replicate API for image upscaling
        const output = await replicate.run(
            "nightmareai/real-esrgan:f121d640bd286e1fdc67f9799164c1d5be36ff74576ee11c803ae5b665dd46aa",
            {
                input: {
                    image: imageData,
                    scale: scale,
                    face_enhance: faceEnhance
                }
            }
        );

        // Handle different output formats from Replicate
        let upscaledImageUrl;

        if (typeof output === 'string') {
            // Direct URL string
            upscaledImageUrl = output;
        } else if (Array.isArray(output) && output.length > 0) {
            // Array of URLs
            upscaledImageUrl = output[0];
        } else if (output && typeof output.url === 'function') {
            // File object with url() method
            upscaledImageUrl = output.url();
        } else if (output && output.url) {
            // Object with url property
            upscaledImageUrl = output.url;
        } else {
            // For streams or other formats
            upscaledImageUrl = output;
        }

        // Return the upscaled image URL
        return res.json({
            success: true,
            upscaledImageUrl: upscaledImageUrl,
            scale: scale,
            faceEnhance: faceEnhance,
            message: 'Image upscaled successfully'
        });

    } catch (error) {
        console.error('❌ Error in upscaleImage:', error);
        return res.status(500).json({
            error: 'Failed to upscale image',
            details: error.message
        });
    }
};



const imageToPrompt = async (req, res) => {
    try {
        const { imageData, model, language, format } = req.body;

        if (!imageData) {
            return res.status(400).json({
                success: false,
                error: 'Image data is required'
            });
        }

        // Set defaults if not provided
        const aiModel = model || 'General Image Prompt';
        const promptLanguage = language || 'English';
        const outputFormat = format || 'Text (TXT)';

        // Initialize Replicate
        const replicate = new Replicate({
            auth: process.env.AI_KEY,
        });

        // Create model-specific prompt instructions
        const getModelSpecificInstructions = (model) => {
            const modelInstructions = {
                'Flux': 'Optimize for Flux AI model - focus on natural language descriptions with emphasis on lighting, composition, and artistic style.',
                'Midjourney': 'Optimize for Midjourney - use descriptive, artistic language with emphasis on style, mood, and visual aesthetics. Include aspect ratios if relevant.',
                'Stable Diffusion': 'Optimize for Stable Diffusion - use clear, detailed descriptions with specific style keywords and technical terms.',
                'DALL-E': 'Optimize for DALL-E - use natural, conversational descriptions focusing on objects, scenes, and artistic styles.',
                'Nano Banana': 'Optimize for Nano Banana model - focus on creative and artistic descriptions with emphasis on visual elements.',
                'Seedream 4': 'Optimize for Seedream 4 - emphasize detailed visual descriptions and artistic composition.',
                'Qwen': 'Optimize for Qwen model - use comprehensive descriptions with focus on visual details and context.',
                'GPT-4o mini': 'Optimize for GPT-4o mini - provide concise yet detailed descriptions suitable for image generation.',
                'General Image Prompt': 'Create a versatile prompt suitable for multiple AI image generation models.'
            };
            return modelInstructions[model] || modelInstructions['General Image Prompt'];
        };

        // Create language-specific instructions
        const getLanguageInstruction = (lang) => {
            if (lang === 'English') return 'Write the prompt in English.';
            return `Write the prompt in ${lang}. Ensure the description is natural and fluent in ${lang}.`;
        };

        // Create format-specific instructions
        const getFormatInstruction = (fmt) => {
            if (fmt === 'JSON') {
                return `Format the output as a JSON object with the following structure:
{
  "prompt": "detailed image description",
  "style": "artistic style or technique",
  "mood": "overall mood or atmosphere",
  "colors": "dominant color palette",
  "composition": "layout and composition details"
}`;
            }
            return 'Return only the text prompt as a single paragraph, nothing else.';
        };

        const input = {
            prompt: `Analyze the provided image and write a detailed text prompt optimized for ${aiModel} that could be used to recreate a visually similar image using an AI image generator.`,
            messages: [],
            verbosity: "medium",
            system_prompt: `
You are an expert prompt engineer and visual interpreter specializing in ${aiModel}.
Your goal is to analyze the uploaded image and generate a detailed, accurate, and creative text prompt that could reproduce the same image using AI image generation models.

SPECIFIC INSTRUCTIONS FOR ${aiModel.toUpperCase()}:
${getModelSpecificInstructions(aiModel)}

LANGUAGE REQUIREMENT:
${getLanguageInstruction(promptLanguage)}

OUTPUT FORMAT:
${getFormatInstruction(outputFormat)}

ANALYSIS RULES:
- Describe the image clearly and objectively: include composition, subject, environment, lighting, style, color palette, text, and mood.
- Use descriptive, evocative language but avoid personal opinions or vague terms like "beautiful" or "cool".
- Text: Describe any text, its color and font-family if visible.
- If identifiable art styles, techniques, or camera settings are visible, describe them precisely (e.g. "digital painting", "macro photography", "cinematic lighting").
- Avoid guessing unknown details; if uncertain, describe what is visually observable instead.
- Do not include camera metadata unless it's visually important.
- If the image style suggests AI art, mention it.

Remember: Generate content in ${promptLanguage} and format according to ${outputFormat} specifications.
`,
            image_input: [imageData],
            reasoning_effort: "medium"
        };

        let generatedPrompt = '';

        for await (const event of replicate.stream("openai/gpt-5-nano", { input })) {
            generatedPrompt += event.toString();
        }

        if (!generatedPrompt) {
            return res.status(500).json({
                success: false,
                error: 'Failed to generate prompt from image'
            });
        }

        res.json({
            success: true,
            prompt: generatedPrompt.trim(),
            config: {
                model: aiModel,
                language: promptLanguage,
                format: outputFormat
            }
        });

    } catch (error) {
        console.error('Error generating prompt from image:', error);
        res.status(500).json({
            success: false,
            error: 'Internal server error'
        });
    }
};

const logoPrompt = async (req, res) => {
    try {
        const { brandName, businessDescription } = req.body;

        if (!brandName || !brandName.trim()) {
            return res.status(400).json({
                success: false,
                error: 'Brand name is required'
            });
        }

        if (!businessDescription || !businessDescription.trim()) {
            return res.status(400).json({
                success: false,
                error: 'Business description is required'
            });
        }

        // Initialize Replicate
        const replicate = new Replicate({
            auth: process.env.AI_KEY,
        });

        const input = {
            prompt: `Brand/Product Name: ${brandName.trim()}
Business Description: ${businessDescription.trim()}

You are Logo Design Ideas, an AI tool that generates creative prompts for logo creation. When a user provides a brand name and business description, respond with exactly 5 unique, detailed prompts optimized for AI image generators like Midjourney, DALL-E, or Stable Diffusion. Each prompt must be distinctly different in style, concept, or approach (e.g., minimalist, illustrative, abstract, typographic, symbolic) to offer variety.`,
            messages: [],
            verbosity: "medium",
            system_prompt: `You are LogoPrompt AI, a specialized assistant that helps users generate creative logo ideas by creating tailored prompts for AI image generators (e.g., Midjourney, DALL-E, or logo-specific tools like Looka). User Input Format: - Brand/Product Name: [The name provided by the user] - Business Description: [A brief description of the business, e.g., "A eco-friendly coffee shop specializing in organic blends"] Your Task: 1. Analyze the brand name and business description to extract key themes, such as industry (e.g., food, tech, fashion), values (e.g., sustainable, innovative), target audience, and visual motifs (e.g., nature for eco-brands, gears for tech). 2. Generate exactly 5 unique, detailed prompts for AI logo generation. Each prompt should: - Incorporate the brand name explicitly (e.g., integrate letters or words into the design). - Suggest 1-2 symbolic elements tied to the business (e.g., coffee beans for a cafe). - Specify a style (e.g., minimalist, vintage, modern, illustrative) to vary the options. - Recommend colors (2-4) that fit the brand's vibe (e.g., earthy greens for eco-friendly). - Ensure the design is simple, vector-friendly, and scalable for logos (avoid clutter; focus on icon + text). - Be phrased as a ready-to-use input for an AI tool, starting with "Create a logo for [Brand Name]:". 3. Vary the 5 prompts to cover different aesthetics: e.g., one minimalist, one bold/3D, one illustrative, one abstract, one retro. 4. Output only the 5 prompts, numbered 1-5. Do not add extra text, explanations, or introductions. Keep each prompt concise yet descriptive (under 100 words). Example Output Structure: 1. Create a logo for [Brand Name]: [Detailed prompt here]... 2. [Next prompt]... ...and so on.`,
            image_input: [],
            reasoning_effort: "minimal"
        };

        let logoPrompts = '';

        for await (const event of replicate.stream("openai/gpt-5-nano", { input })) {
            logoPrompts += event.toString();
        }

        if (!logoPrompts) {
            return res.status(500).json({
                success: false,
                error: 'Failed to generate logo prompts'
            });
        }

        res.json({
            success: true,
            logoPrompts: logoPrompts.trim(),
            brandName: brandName.trim(),
            businessDescription: businessDescription.trim()
        });

    } catch (error) {
        console.error('Error generating logo prompts:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to generate logo prompts',
            details: error.message
        });
    }
};

const imagePromptGenerator = async (req, res) => {
    try {
        const { rawPrompt, selectedStyle } = req.body;

        if (!rawPrompt || !rawPrompt.trim()) {
            return res.status(400).json({
                success: false,
                error: 'Raw prompt is required'
            });
        }

        if (!selectedStyle || !selectedStyle.trim()) {
            return res.status(400).json({
                success: false,
                error: 'Image style selection is required'
            });
        }

        const replicate = new Replicate({
            auth: process.env.AI_KEY,
        });

        const input = {
            prompt: `Raw Prompt: ${rawPrompt.trim()}
Style: ${selectedStyle.trim()}

Transform this into a detailed image generation prompt incorporating the selected style.`,
            messages: [],
            verbosity: "medium",
            system_prompt: `You are an expert AI image generation prompt engineer. Your task is to transform basic user ideas into detailed, high-quality prompts optimized for AI image generation models like DALL-E, Midjourney, or Stable Diffusion. When a user provides a raw prompt and selects a style, you will:

1. Enhance the core concept with vivid, specific details
2. Incorporate the selected style seamlessly
3. Add relevant artistic techniques, lighting, composition, and quality modifiers
4. Structure the prompt for optimal AI image generation results

Guidelines:
- Keep the user's core concept intact while enriching it
- Use specific, descriptive language rather than vague terms
- Include relevant artistic elements (lighting, camera angles, color palette, mood)
- Add quality enhancers (e.g., "highly detailed", "8k", "professional photography")
- Maintain coherent flow and avoid contradictory elements
- Format: Present the enhanced prompt as a single, comma-separated paragraph

Do not include:
- Negative prompts or exclusions
- Technical parameters (CFG scale, steps, etc.)
- Multiple variations - provide one optimized prompt
- Explanations or commentary - return only the enhanced prompt`,
            image_input: [],
            reasoning_effort: "medium"
        };

        let enhancedPrompt = '';

        for await (const event of replicate.stream("openai/gpt-5-nano", { input })) {
            enhancedPrompt += event.toString();
        }

        res.json({
            success: true,
            enhancedPrompt: enhancedPrompt.trim(),
            rawPrompt: rawPrompt.trim(),
            selectedStyle: selectedStyle.trim()
        });

    } catch (error) {
        console.error('Error generating enhanced prompt:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to generate enhanced prompt',
            details: error.message
        });
    }
};

module.exports = {
    getResult,
    removeBackground,
    upscaleImage,
    imageToPrompt,
    logoPrompt,
    imagePromptGenerator
};

