import React, { useState, useRef, useEffect } from 'react';
import {
  Upload,
  X,
  Download,
  ChevronLeft,
  ChevronRight,
  RefreshCcw,
  Loader2,
  MessageCircle,
  ChevronDown
} from 'lucide-react';
import axios from 'axios';
import SEO, { pageSEO } from '../components/SEO';
import ParticleBackground from '../components/ParticleBackground';

// FeatureCard Component
function FeatureCard({ icon, title, description }) {
  return (
    <div className="bg-gray-800/30 backdrop-blur-md rounded-2xl p-6 border border-gray-700/30 hover:border-purple-500/30 transition-all duration-300 group">
      <div className="text-4xl mb-4 group-hover:scale-110 transition-transform duration-300">
        {icon}
      </div>
      <h3 className="text-xl font-semibold text-white mb-3 group-hover:text-purple-300 transition-colors duration-300">
        {title}
      </h3>
      <p className="text-gray-400 leading-relaxed">
        {description}
      </p>
    </div>
  );
}

// FAQItem Component
function FAQItem({ question, answer, isOpen, onClick }) {
  return (
    <div className="bg-gray-800/30 backdrop-blur-md rounded-2xl border border-gray-700/30 overflow-hidden hover:border-purple-500/30 transition-all duration-300">
      <button
        onClick={onClick}
        className="w-full px-6 py-5 text-left flex items-center justify-between hover:bg-gray-700/20 transition-colors duration-200"
      >
        <h3 className="text-lg font-semibold text-white pr-4">{question}</h3>
        <ChevronDown
          className={`w-5 h-5 text-purple-400 transition-transform duration-300 flex-shrink-0 ${
            isOpen ? 'rotate-180' : ''
          }`}
        />
      </button>
      {isOpen && (
        <div className="px-6 pb-5">
          <div className="pt-2 border-t border-gray-700/30">
            <p className="text-gray-300 leading-relaxed">{answer}</p>
          </div>
        </div>
      )}
    </div>
  );
}

const UpscaleImage = () => {
  const [file, setFile] = useState(null);
  const [status, setStatus] = useState('idle'); // idle, processing, done
  const [dragActive, setDragActive] = useState(false);
  const [sliderPosition, setSliderPosition] = useState(50);
  const [resolution, setResolution] = useState('2k'); // Default to 2k
  const [upscaledImage, setUpscaledImage] = useState(null);
  const [error, setError] = useState(null);
  const [isDownloading, setIsDownloading] = useState(false);
  const imageContainerRef = useRef(null);

  // FAQ State
  const [openFAQ, setOpenFAQ] = useState(0);

  // Demo image
  const demoImage = "https://images.unsplash.com/photo-1474511320723-9a56873867b5?q=80&w=1172&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D";
  const demoEnhancedImage = "https://images.unsplash.com/photo-1474511320723-9a56873867b5?q=80&w=2344&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D";

  // Images for slider - proper logic for before/after states
  const beforeImage = file?.url || demoImage; // Original uploaded image or demo
  const afterImage = upscaledImage || demoEnhancedImage; // Only show upscaled result or demo enhanced, never uploaded image

  // Images for decorative pills - always show demo images
  const cardBeforeImage = demoImage;
  const cardAfterImage = demoEnhancedImage;

  // Update status when upscaled image is received
  useEffect(() => {
    if (upscaledImage && status === 'processing') {
      setStatus('done');
      setSliderPosition(50); // Reset slider for new image
    }
  }, [upscaledImage, status]);

  // Reset slider position when upscaled image arrives
  useEffect(() => {
    if (upscaledImage) {
      setSliderPosition(50); // Center the slider to show the comparison
    }
  }, [upscaledImage]);

  const handleDrag = (e) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  };

  const handleDrop = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      const file = e.dataTransfer.files[0];
      if (isValidImageFile(file)) {
        handleFile(file);
      }
    }
  };

  const handleFileInput = (e) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      if (isValidImageFile(file)) {
        handleFile(file);
      }
    }
  };

  // Validate image file type and show error for unsupported formats
  const isValidImageFile = (file) => {
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];

    // Check if file type is in allowed list
    if (!allowedTypes.includes(file.type)) {
      alert('❌ Unsupported file format. Please upload JPG, JPEG, PNG, or WebP images only.');
      return false;
    }

    // Check file size (max 10MB)
    const maxSize = 10 * 1024 * 1024; // 10MB in bytes
    if (file.size > maxSize) {
      alert('❌ File size too large. Please upload images smaller than 10MB.');
      return false;
    }

    return true;
  };

  const handleFile = (uploadedFile) => {
    // Create a preview URL
    const url = URL.createObjectURL(uploadedFile);
    setFile({ file: uploadedFile, url });
    setStatus('processing');
    // Start actual processing
    processImage(uploadedFile);
  };

  const convertImageToBase64 = (file) => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => resolve(reader.result);
      reader.onerror = reject;
      reader.readAsDataURL(file);
    });
  };

  const processImage = async (uploadedFile) => {
    try {
      // Convert image to base64
      const imageBase64 = await convertImageToBase64(uploadedFile);

      // Determine scale based on resolution
      const scale = resolution === '4k' ? 4 : 2;

      // Call the backend API
      const response = await axios.post(`${import.meta.env.VITE_API_BASE_URL}/api/ai/upscale-image`, {
        imageData: imageBase64,
        scale: scale,
        faceEnhance: false
      });

      if (response.data.success) {
        setUpscaledImage(response.data.upscaledImageUrl);
        setStatus('done');
        setError(null);
      } else {
        setError(response.data.error || 'Failed to upscale image');
        setStatus('idle');
      }
    } catch (error) {
      console.error('Error upscaling image:', error);
      setError(error.response?.data?.error || 'Failed to upscale image');
      setStatus('idle');
    }
  };

  const handleDownload = async () => {
    if (!upscaledImage) {
      setError('No upscaled image available for download');
      return;
    }

    setIsDownloading(true);
    setError(null);

    try {
      // Fetch the upscaled image from the URL
      const response = await fetch(upscaledImage);
      if (!response.ok) {
        throw new Error('Failed to fetch upscaled image');
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);

      // Create download link
      const link = document.createElement('a');
      link.href = url;

      // Generate filename based on original file name or default
      const originalName = file?.file?.name || 'image';
      const nameWithoutExt = originalName.replace(/\.[^/.]+$/, "");
      const resolutionLabel = resolution === '4k' ? '4K' : '2K';
      link.download = `${nameWithoutExt}-upscaled-${resolutionLabel}.png`;

      // Trigger download
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // Clean up the blob URL
      window.URL.revokeObjectURL(url);

    } catch (error) {
      console.error('Error downloading image:', error);
      setError('Failed to download upscaled image');
    } finally {
      setIsDownloading(false);
    }
  };

  const handleReset = () => {
    setFile(null);
    setStatus('idle');
    setSliderPosition(50);
    setUpscaledImage(null);
    setError(null);
  };

  const handleSliderMove = (e) => {
    if (!imageContainerRef.current) return;

    const { left, width } = imageContainerRef.current.getBoundingClientRect();
    const clientX = e.touches ? e.touches[0].clientX : e.clientX;

    // Calculate percentage (0 to 100)
    let pos = ((clientX - left) / width) * 100;

    // Clamp values
    pos = Math.max(0, Math.min(100, pos));
    setSliderPosition(pos);
  };

  return (
    <div className="min-h-screen relative text-slate-100 font-sans flex flex-col items-center justify-center p-4 selection:bg-purple-500/30">
      <SEO {...pageSEO.upscaleImage} />

      {/* Particle Background */}
      <ParticleBackground />

      {/* Background gradient - Dark theme like PhotoGPT */}
      <div className="absolute inset-0 bg-gradient-to-br from-gray-900 via-gray-800 to-black -z-10"></div>

      {/* Subtle pattern overlay */}
      <div className="absolute inset-0 opacity-10 -z-5" style={{
        backgroundImage: `radial-gradient(circle at 25% 25%, rgba(59, 130, 246, 0.3) 0%, transparent 50%),
                         radial-gradient(circle at 75% 75%, rgba(147, 51, 234, 0.3) 0%, transparent 50%)`
      }}></div>

      {/* Background Decor */}
      <div className="fixed top-0 left-0 w-full h-full overflow-hidden pointer-events-none z-0">
         <div className="absolute top-[-10%] left-[-10%] w-[40%] h-[40%] bg-purple-900/10 rounded-full blur-[100px]"></div>
         <div className="absolute bottom-[-10%] right-[-10%] w-[40%] h-[40%] bg-pink-900/10 rounded-full blur-[100px]"></div>
      </div>

      {/* Header Section */}
      <div className="relative z-10 text-center max-w-3xl mx-auto mb-12 space-y-4 animate-in fade-in slide-in-from-top-8 duration-700">
        <h1 className="text-4xl md:text-5xl lg:text-6xl font-extrabold tracking-tight text-white drop-shadow-sm">
          Free Online <span className="text-transparent bg-clip-text bg-gradient-to-r from-purple-400 to-pink-600">Image Upscaler</span>
        </h1>
        <p className="text-lg md:text-xl text-slate-400 leading-relaxed max-w-2xl mx-auto">
          AI image upscaler will enlarge and sharpen your photo in a single click. Increase image quality to 4K with our free online tool! No user login needed!
        </p>
      </div>

      <div className="max-w-7xl w-full grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-16 items-center relative z-10">

        {/* LEFT SIDE: Interactive Slider (Always Visible) */}
        <div className="flex flex-col items-center justify-center w-full animate-in slide-in-from-left duration-500">

           <div
             ref={imageContainerRef}
             className="relative w-full rounded-2xl overflow-hidden bg-slate-900 shadow-2xl ring-1 ring-white/10 select-none cursor-col-resize touch-none group"
             onMouseMove={(e) => e.buttons === 1 && handleSliderMove(e)}
             onTouchMove={handleSliderMove}
             onClick={handleSliderMove}
           >
              {/* RIGHT LAYER (AFTER/ENHANCED) - Base Layer */}
              <img
                 src={afterImage}
                 alt="After - Enhanced"
                 className="block w-full h-auto pointer-events-none"
              />

              {/* Processing Overlay for After Side */}
              {status === 'processing' && (
                <div className="absolute inset-0 bg-slate-900/80 backdrop-blur-sm flex items-center justify-center pointer-events-none z-10">
                  <div className="flex flex-col items-center space-y-3">
                    <div className="w-8 h-8 border-2 border-purple-500 border-t-transparent rounded-full animate-spin"></div>
                    <span className="text-white text-xs font-medium">Processing...</span>
                  </div>
                </div>
              )}

              {/* After Label */}
              <div className="absolute top-4 right-4 bg-black/40 backdrop-blur-sm border border-white/10 text-white/90 text-[10px] font-bold px-2 py-1 rounded-md uppercase tracking-wider pointer-events-none z-20">
                 {upscaledImage ? 'Enhanced' : status === 'processing' ? 'Processing' : 'After'}
              </div>

              {/* LEFT LAYER (BEFORE/ORIGINAL) - Clipped Layer */}
              <div
                className="absolute inset-0 w-full h-full overflow-hidden pointer-events-none transition-all duration-300 ease-out"
                style={{ clipPath: `inset(0 ${100 - sliderPosition}% 0 0)` }}
              >
                 <img
                   src={beforeImage}
                   alt="Before - Original"
                   className="absolute inset-0 w-full h-full object-cover"
                 />
                 {/* Before Label */}
                 <div className="absolute top-4 left-4 bg-black/40 backdrop-blur-sm border border-white/10 text-white/90 text-[10px] font-bold px-2 py-1 rounded-md uppercase tracking-wider">
                    {file ? 'Original' : 'Before'}
                 </div>
              </div>

              {/* SLIDER HANDLE */}
              <div
                 className="absolute top-0 bottom-0 w-0.5 bg-white shadow-[0_0_10px_rgba(0,0,0,0.5)] z-20 pointer-events-none transition-all duration-300 ease-out"
                 style={{ left: `${sliderPosition}%` }}
              >
                 <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-8 h-8 bg-white rounded-full flex items-center justify-center shadow-lg text-slate-800 transform transition-transform group-hover:scale-110">
                    <div className="flex -space-x-1">
                      <ChevronLeft className="w-4 h-4" />
                      <ChevronRight className="w-4 h-4" />
                    </div>
                 </div>
              </div>
           </div>

           {/* Caption */}
           <p className="mt-6 text-slate-400 text-sm font-medium tracking-wide uppercase opacity-60">
              {upscaledImage ? 'Drag to compare original vs enhanced' : file ? 'Upload complete - processing will show results here' : 'Interactive Demo Preview'}
           </p>

        </div>

        {/* RIGHT SIDE: Controls (Upload / Status / Download) */}
        <div className="flex flex-col items-center w-full">

          <div className="w-full max-w-md bg-slate-900/80 backdrop-blur-xl border border-slate-700/50 rounded-3xl shadow-2xl overflow-hidden p-8 flex flex-col items-center">

             {/* Header */}
             <div className="flex flex-col items-center mb-8">
                 {/* Decorative Pills with Images */}
                 <div className="flex items-center space-x-3 mb-4">
                    <div className="flex flex-col items-center">
                       <div className="w-12 h-8 rounded bg-slate-800 border border-slate-700 overflow-hidden relative">
                          <img
                            src={cardBeforeImage}
                            alt="Before Tiny"
                            className="w-full h-full object-cover opacity-60"
                          />
                       </div>
                       <span className="text-[8px] uppercase font-bold text-slate-500 mt-1">Before</span>
                    </div>

                    <div className="w-6 h-[1px] bg-slate-700"></div>

                    <div className="flex flex-col items-center">
                       <div className="w-12 h-8 rounded bg-slate-800 border border-purple-500/50 overflow-hidden relative shadow-lg shadow-purple-900/20">
                          <img
                            src={cardAfterImage}
                            alt="After Tiny"
                            className="w-full h-full object-cover"
                          />
                       </div>
                       <span className="text-[8px] uppercase font-bold text-purple-400 mt-1">After</span>
                    </div>
                 </div>

                 <h1 className="text-3xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-white to-slate-400 text-center">
                    AI Enhance
                 </h1>

                 {/* Resolution Selector (Only visible in Idle state) */}
                 {status === 'idle' && (
                    <div className="flex items-center bg-slate-800/50 p-1 rounded-xl border border-slate-700/50 mt-6">
                       <button
                          onClick={() => setResolution('2k')}
                          className={`
                             px-5 py-2 rounded-lg text-xs font-bold transition-all duration-300
                             ${resolution === '2k'
                                ? 'bg-slate-700 text-white shadow-md'
                                : 'text-slate-400 hover:text-slate-200 hover:bg-slate-800/50'}
                          `}
                       >
                          2K Resolution
                       </button>
                       <button
                          onClick={() => setResolution('4k')}
                          className={`
                             px-5 py-2 rounded-lg text-xs font-bold transition-all duration-300 relative
                             ${resolution === '4k'
                                ? 'bg-gradient-to-r from-purple-600 to-pink-600 text-white shadow-lg shadow-purple-900/20'
                                : 'text-slate-400 hover:text-slate-200 hover:bg-slate-800/50'}
                          `}
                       >
                          4K Ultra HD
                       </button>
                    </div>
                 )}

                 {/* Processing State for AI Enhance Card */}
                 {status === 'processing' && (
                    <div className="flex flex-col items-center space-y-4 mt-6">
                       <div className="w-16 h-16 border-3 border-purple-500 border-t-transparent rounded-full animate-spin"></div>
                       <div className="text-center">
                          <p className="text-white font-semibold text-sm">Processing Image</p>
                          <p className="text-slate-400 text-xs mt-1">AI enhancement in progress...</p>
                       </div>
                    </div>
                 )}
             </div>

             {/* Dynamic Content Area */}
             <div className="w-full">

                {/* STATE: IDLE (Upload) */}
                {status === 'idle' && (
                  <div
                    className={`
                      relative border-2 border-dashed rounded-2xl h-64 flex flex-col items-center justify-center text-center transition-all duration-200
                      ${dragActive ? 'border-purple-500 bg-purple-500/10' : 'border-slate-700 hover:border-slate-600 bg-slate-800/30'}
                    `}
                    onDragEnter={handleDrag}
                    onDragLeave={handleDrag}
                    onDragOver={handleDrag}
                    onDrop={handleDrop}
                  >
                    <input
                      type="file"
                      className="hidden"
                      id="file-upload"
                      accept="image/jpeg,image/jpg,image/png,image/webp"
                      onChange={handleFileInput}
                    />

                    <label
                      htmlFor="file-upload"
                      className="cursor-pointer group relative overflow-hidden rounded-full bg-gradient-to-r from-purple-600 to-pink-600 px-8 py-3.5 font-bold text-white shadow-lg shadow-purple-900/20 transition-transform hover:scale-105 active:scale-95 flex items-center space-x-2"
                    >
                      <Upload className="w-5 h-5" />
                      <span>Browse files</span>
                    </label>

                    <p className="mt-6 text-slate-400 text-sm font-medium">
                       Drop, paste or add a URL
                    </p>

                    <p className="mt-4 text-xs text-slate-500">
                      Supported formats: JPG, JPEG, PNG, WebP (max 10MB)
                    </p>

                    <p className="mt-4 text-[10px] text-slate-600 max-w-[200px] mx-auto leading-relaxed">
                       By uploading a file, you agree to our <a href="/terms" className="underline hover:text-purple-400">Terms of Use</a> and <a href="/terms" className="underline hover:text-purple-400">Privacy Policy</a>.
                    </p>
                  </div>
                )}

                {/* STATE: PROCESSING */}
                {status === 'processing' && (
                   <div className="h-64 flex flex-col items-center justify-center border-2 border-dashed border-slate-700/50 rounded-2xl bg-slate-800/20 w-full">
                      <div className="relative">
                         <div className="w-16 h-16 rounded-full border-4 border-slate-700 border-t-purple-500 animate-spin"></div>
                         <div className="absolute inset-0 flex items-center justify-center">
                            <div className="w-8 h-8 rounded-full bg-purple-500/20 blur-md"></div>
                         </div>
                      </div>
                      <p className="mt-6 text-slate-300 font-medium animate-pulse">Processing your image...</p>
                   </div>
                )}

                {/* STATE: DONE (Download) */}
                {status === 'done' && upscaledImage && (
                   <div className="flex flex-col space-y-4 w-full animate-in fade-in zoom-in duration-300">

                      <div className="p-4 bg-slate-800/50 rounded-xl border border-slate-700 flex items-center space-x-4">
                         <div className="w-16 h-16 bg-slate-900 rounded-lg overflow-hidden border border-slate-700 shrink-0">
                            <img src={file?.url} className="w-full h-full object-cover opacity-80" alt="Thumbnail" />
                         </div>
                         <div className="flex-1 min-w-0">
                            <h3 className="text-white font-medium truncate">{file?.file.name}</h3>
                            <p className="text-green-400 text-xs font-bold uppercase mt-1">Enhancement Complete</p>
                         </div>
                      </div>

                      <button
                         onClick={handleDownload}
                         disabled={isDownloading}
                         className="w-full flex items-center justify-center space-x-2 py-4 px-6 rounded-xl bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-500 hover:to-pink-500 disabled:from-gray-600 disabled:to-gray-700 text-white font-bold text-lg shadow-xl shadow-purple-900/30 transition-all hover:scale-[1.02] active:scale-[0.98] disabled:cursor-not-allowed disabled:hover:scale-100"
                      >
                         {isDownloading ? (
                           <>
                             <Loader2 className="w-5 h-5 animate-spin" />
                             <span>Downloading...</span>
                           </>
                         ) : (
                           <>
                             <Download className="w-5 h-5" />
                             <span>Download Image</span>
                           </>
                         )}
                      </button>

                      <button
                         onClick={handleReset}
                         className="w-full flex items-center justify-center space-x-2 py-3 px-4 rounded-xl bg-slate-800 hover:bg-slate-700 text-slate-300 font-medium transition-colors"
                      >
                         <RefreshCcw className="w-4 h-4" />
                         <span>Enhance another photo</span>
                      </button>

                   </div>
                )}

             </div>
          </div>
        </div>

      </div>

      {/* What is Image Upscaler Section */}
      <div className="max-w-7xl mx-auto px-4 py-20 sm:px-6 lg:px-8">
        {/* Header Section */}
        <div className="text-center max-w-4xl mx-auto mb-20">
          <h1 className="text-5xl md:text-6xl lg:text-7xl font-bold text-white mb-8">
            What is <span className="text-purple-400 inline-block relative">
              Image Upscaler?
              <span className="absolute bottom-0 left-0 w-full h-2 bg-purple-500/20 rounded-full"></span>
            </span>
          </h1>
          <p className="text-xl md:text-2xl text-gray-400 leading-relaxed">
            Image Upscaler uses advanced AI technology to enhance and enlarge images while preserving quality and detail. Transform low-resolution photos into crisp, high-definition images perfect for printing, web use, and professional projects.
          </p>
        </div>

        {/* Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-32">
          <FeatureCard
            icon="🔍"
            title="AI-Powered Enhancement"
            description="Advanced machine learning algorithms analyze and enhance every pixel for superior image quality."
          />
          <FeatureCard
            icon="📐"
            title="Multiple Resolutions"
            description="Upscale to 2K or 4K resolution while maintaining sharp details and natural textures."
          />
          <FeatureCard
            icon="🖼️"
            title="Preserve Quality"
            description="Smart enhancement technology prevents pixelation and maintains image clarity at any size."
          />
          <FeatureCard
            icon="⚡"
            title="Fast Processing"
            description="Get enhanced images in seconds with our optimized AI processing pipeline."
          />
          <FeatureCard
            icon="🎨"
            title="All Image Types"
            description="Works with photos, artwork, graphics, and any image format you need to enhance."
          />
          <FeatureCard
            icon="💎"
            title="Professional Results"
            description="Achieve print-quality results suitable for professional use, marketing, and large displays."
          />
        </div>

        {/* FAQ Section */}
        <div className="max-w-5xl mx-auto mb-20">
          <div className="text-center mb-16">
            <h2 className="text-5xl md:text-6xl font-bold text-white mb-6">Frequently Asked Questions</h2>
            <p className="text-xl text-gray-400">Everything you need to know about Image Upscaler AI</p>
          </div>

          <div className="space-y-6">
            {[
              {
                question: "What's the difference between 2K and 4K upscaling?",
                answer: "2K upscaling doubles your image resolution (2x), while 4K upscaling quadruples it (4x). Choose 2K for web use and 4K for high-quality printing or large displays."
              },
              {
                question: "What image formats are supported?",
                answer: "We support JPG, JPEG, PNG, and WebP formats. Maximum file size is 10MB per image for optimal processing speed."
              },
              {
                question: "How does AI upscaling work?",
                answer: "Our AI analyzes patterns in your image and intelligently fills in missing details when enlarging, rather than simply stretching pixels like traditional methods."
              },
              {
                question: "Will upscaling work on old or blurry photos?",
                answer: "Yes! Our AI is particularly effective at enhancing old, low-resolution, or slightly blurry photos by reconstructing lost detail and sharpening edges."
              },
              {
                question: "Is there a limit to how many images I can upscale?",
                answer: "No limits! Upscale as many images as you need, completely free. All processing is done securely and your images are automatically deleted after processing."
              },
              {
                question: "How long does the upscaling process take?",
                answer: "Most images are processed within 10-30 seconds, depending on the original size and chosen resolution. Larger files may take slightly longer."
              }
            ].map((faq, index) => (
              <FAQItem
                key={index}
                question={faq.question}
                answer={faq.answer}
                isOpen={openFAQ === index}
                onClick={() => setOpenFAQ(openFAQ === index ? -1 : index)}
              />
            ))}
          </div>
        </div>

        {/* CTA Section */}
        <div className="max-w-4xl mx-auto">
          <div className="bg-gradient-to-r from-[#1a1a23] to-purple-950/50 border border-purple-500/20 rounded-3xl p-8 md:p-12 text-center relative overflow-hidden">
            {/* Background decoration */}
            <div className="absolute top-0 left-0 w-full h-full bg-[radial-gradient(circle_at_50%_0%,rgba(147,51,234,0.15),transparent_50%)]"></div>

            <div className="relative z-10">
              <h2 className="text-2xl md:text-3xl font-bold text-white mb-4">Need More Help?</h2>
              <p className="text-gray-400 mb-8 max-w-lg mx-auto">
                Can't find the answer you're looking for? Our support team is ready to assist you with any questions about Image Upscaler AI.
              </p>
              <button className="bg-gray-200 hover:bg-white text-gray-900 font-semibold py-3 px-8 rounded-lg transition-colors duration-200 inline-flex items-center gap-2">
                <MessageCircle className="w-5 h-5" />
                Contact Support
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default UpscaleImage;
