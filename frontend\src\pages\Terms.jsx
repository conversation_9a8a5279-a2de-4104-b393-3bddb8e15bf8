import React, { useEffect } from 'react';
import { ArrowLeft, Shield, AlertTriangle, Users, FileText, DollarSign, Home, Sparkles, Scale, Eye, Lock, Globe, Calendar, Mail, CheckCircle, XCircle, Info } from 'lucide-react';
import { Link } from 'react-router-dom';
import SEO, { pageSEO } from '../components/SEO';
import Breadcrumbs, { BreadcrumbStructuredData } from '../components/Breadcrumbs';

const Terms = () => {
  // Scroll to top when component mounts
  useEffect(() => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  }, []);

  // Create breadcrumbs for terms page
  const termsBreadcrumbs = [
    { label: 'Home', href: '/', icon: Home },
    { label: 'Terms & Conditions', href: null, icon: FileText }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 py-8 relative overflow-hidden">
      {/* SEO */}
      <SEO
        title={pageSEO.terms.title}
        description={pageSEO.terms.description}
        keywords={pageSEO.terms.keywords}
      />

      {/* Structured Data for Breadcrumbs */}
      <BreadcrumbStructuredData breadcrumbs={termsBreadcrumbs} />

      {/* Background Elements */}
      <div className="fixed inset-0 pointer-events-none overflow-hidden -z-10">
        <div className="absolute top-20 left-10 w-32 h-32 bg-blue-800 rounded-full opacity-10 animate-float" style={{ animationDelay: '0s' }}></div>
        <div className="absolute top-40 right-20 w-24 h-24 bg-purple-800 rounded-full opacity-10 animate-float" style={{ animationDelay: '2s' }}></div>
        <div className="absolute bottom-40 left-20 w-20 h-20 bg-green-800 rounded-full opacity-10 animate-float" style={{ animationDelay: '4s' }}></div>
        <div className="absolute bottom-20 right-10 w-28 h-28 bg-orange-800 rounded-full opacity-10 animate-float" style={{ animationDelay: '6s' }}></div>
      </div>

      <div className="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        {/* Breadcrumbs */}
        <Breadcrumbs customBreadcrumbs={termsBreadcrumbs} />

        {/* Enhanced Header */}
        <div className="text-center mb-16">
          <div className="relative inline-block mb-8 animate-bounce-in">
            <div className="w-20 h-20 sm:w-24 sm:h-24 bg-gradient-to-br from-blue-900 via-purple-900 to-pink-900 rounded-3xl flex items-center justify-center shadow-lg">
              <Scale className="w-10 h-10 sm:w-12 sm:h-12 text-blue-400" />
            </div>
            <div className="absolute -top-2 -right-2 animate-bounce">
              <Sparkles className="w-6 h-6 text-yellow-500" />
            </div>
          </div>

          <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold  mb-6">
            Terms & Conditions
          </h1>

          <p className="text-xl sm:text-2xl text-gray-300 mb-8 max-w-3xl mx-auto leading-relaxed">
            Your rights, responsibilities, and our commitment to fair service
          </p>

          {/* Quick Info Pills */}
          <div className="flex flex-wrap justify-center gap-4 mb-8">
            <div className="flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-blue-900 to-purple-900 rounded-full shadow-lg">
              <Calendar className="w-5 h-5 text-blue-400" />
              <span className="font-semibold text-blue-300">Last Updated: 09/12/2025</span>
            </div>
            <div className="flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-green-900 to-emerald-900 rounded-full shadow-lg">
              <Globe className="w-5 h-5 text-green-400" />
              <span className="font-semibold text-green-300">Worldwide Service</span>
            </div>
          </div>

          {/* Back to Home Button */}
          <Link
            to="/"
            className="inline-flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-medium rounded-xl transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105"
          >
            <ArrowLeft className="w-5 h-5" />
            Back to Home
          </Link>
        </div>

        {/* Enhanced Content */}
        <div className="space-y-8">

          {/* Acceptance of Terms */}
          <section className="bg-gradient-to-br from-gray-800 via-blue-900/20 to-gray-800 rounded-3xl shadow-2xl p-8 sm:p-12 border border-blue-800 animate-fade-in-up">
            <div className="flex items-center gap-4 mb-6">
              <div className="p-3 bg-gradient-to-r from-blue-500 to-purple-500 rounded-2xl shadow-lg">
                <FileText className="w-8 h-8 text-white" />
              </div>
              <h2 className="text-3xl sm:text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                1. Acceptance of Terms
              </h2>
            </div>
            <div className="text-gray-300 space-y-4 text-lg leading-relaxed">
              <p className="font-medium text-gray-200">
                By accessing and using these AI tools and services, you accept and agree to be bound by the terms and provision of this agreement.
              </p>
              <div className="bg-blue-900/30 rounded-2xl p-4 border border-blue-700">
                <p className="text-blue-200 font-medium">
                  If you do not agree to abide by the above, please do not use this service.
                </p>
              </div>
            </div>
          </section>

          {/* Service Overview */}
          <section className="bg-gradient-to-br from-gray-800 via-purple-900/20 to-gray-800 rounded-3xl shadow-2xl p-8 sm:p-12 border border-purple-800 animate-fade-in-up">
            <div className="flex items-center gap-4 mb-6">
              <div className="p-3 bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl shadow-lg">
                <Sparkles className="w-8 h-8 text-white" />
              </div>
              <h2 className="text-3xl sm:text-4xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                2. Our AI Tools
              </h2>
            </div>
            <div className="text-gray-300 space-y-6 text-lg leading-relaxed">
              <p className="font-medium text-gray-200">
                Rright now GenFreeAI provides 5 specialized AI-powered tools designed to enhance your creative workflow:
              </p>
              <div className="grid md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <h3 className="text-xl font-semibold text-purple-400 flex items-center gap-2">
                    <Sparkles className="w-5 h-5" />
                    Image Creation & Enhancement
                  </h3>
                  <div className="space-y-3">
                    <div className="p-4 bg-purple-900/20 rounded-xl border border-purple-700">
                      <h4 className="font-semibold text-purple-300 mb-2">1. Background Remover</h4>
                      <p className="text-sm text-gray-300">Upload any image and automatically remove its background with precision.</p>
                    </div>
                    <div className="p-4 bg-purple-900/20 rounded-xl border border-purple-700">
                      <h4 className="font-semibold text-purple-300 mb-2">2. Image Upscaler</h4>
                      <p className="text-sm text-gray-300">Upload images and enhance their resolution and quality using AI technology.</p>
                    </div>
                  </div>
                </div>
                <div className="space-y-4">
                  <h3 className="text-xl font-semibold text-pink-400 flex items-center gap-2">
                    <Scale className="w-5 h-5" />
                    Prompt Generation Tools
                  </h3>
                  <div className="space-y-3">
                    <div className="p-4 bg-pink-900/20 rounded-xl border border-pink-700">
                      <h4 className="font-semibold text-pink-300 mb-2">3. Image to Prompt Generator</h4>
                      <p className="text-sm text-gray-300">Upload any image and receive detailed prompts to recreate it with AI.</p>
                    </div>
                    <div className="p-4 bg-pink-900/20 rounded-xl border border-pink-700">
                      <h4 className="font-semibold text-pink-300 mb-2">4. Logo Prompt Generator</h4>
                      <p className="text-sm text-gray-300">Provide your brand name and description to get optimized logo creation prompts.</p>
                    </div>
                    <div className="p-4 bg-pink-900/20 rounded-xl border border-pink-700">
                      <h4 className="font-semibold text-pink-300 mb-2">5. Image Prompt Generator</h4>
                      <p className="text-sm text-gray-300">Transform your basic prompts into detailed, optimized AI image prompts.</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </section>

          {/* Use of Service */}
          <section className="bg-gradient-to-br from-gray-800 via-green-900/20 to-gray-800 rounded-3xl shadow-2xl p-8 sm:p-12 border border-green-800 animate-fade-in-up">
            <div className="flex items-center gap-4 mb-6">
              <div className="p-3 bg-gradient-to-r from-green-500 to-emerald-500 rounded-2xl shadow-lg">
                <CheckCircle className="w-8 h-8 text-white" />
              </div>
              <h2 className="text-3xl sm:text-4xl font-bold bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent">
                3. Use of Service
              </h2>
            </div>
            <div className="text-gray-300 space-y-6 text-lg leading-relaxed">
              <p className="font-medium text-gray-200">
                Our platform provides free AI-powered tools for personal and commercial use. You may use these services to:
              </p>
              <div className="grid sm:grid-cols-2 gap-4">

                <div className="flex items-center gap-3 p-3 bg-green-900/20 rounded-xl border border-green-700">
                  <CheckCircle className="w-5 h-5 text-green-400 flex-shrink-0" />
                  <span><strong>Background Remover:</strong> Remove backgrounds from uploaded images</span>
                </div>
                <div className="flex items-center gap-3 p-3 bg-green-900/20 rounded-xl border border-green-700">
                  <CheckCircle className="w-5 h-5 text-green-400 flex-shrink-0" />
                  <span><strong>Image Upscaler:</strong> Enhance image resolution and quality</span>
                </div>
                <div className="flex items-center gap-3 p-3 bg-green-900/20 rounded-xl border border-green-700">
                  <CheckCircle className="w-5 h-5 text-green-400 flex-shrink-0" />
                  <span><strong>Image to Prompt:</strong> Generate prompts from uploaded images</span>
                </div>
                <div className="flex items-center gap-3 p-3 bg-green-900/20 rounded-xl border border-green-700">
                  <CheckCircle className="w-5 h-5 text-green-400 flex-shrink-0" />
                  <span><strong>Logo Prompt Generator:</strong> Create logo prompts from brand details</span>
                </div>
                <div className="flex items-center gap-3 p-3 bg-green-900/20 rounded-xl border border-green-700">
                  <CheckCircle className="w-5 h-5 text-green-400 flex-shrink-0" />
                  <span><strong>Image Prompt Generator:</strong> Transform basic prompts into detailed ones</span>
                </div>


                 <div className="flex items-center gap-3 p-3 bg-green-900/20 rounded-xl border border-green-700">
                  <CheckCircle className="w-5 h-5 text-green-400 flex-shrink-0" />
                  <span><strong>In the future:</strong> We will add more tools</span>
                </div>

              </div>
              <div className="bg-green-900/30 rounded-2xl p-4 border border-green-700">
                <p className="text-green-200 font-medium">
                  ✨ All tools are free to use for both personal and commercial purposes with proper attribution.
                </p>
              </div>
            </div>
          </section>

          {/* Prohibited Uses */}
          <section className="bg-gradient-to-br from-gray-800 via-red-900/20 to-gray-800 rounded-3xl shadow-2xl p-8 sm:p-12 border border-red-800 animate-fade-in-up">
            <div className="flex items-center gap-4 mb-6">
              <div className="p-3 bg-gradient-to-r from-red-500 to-pink-500 rounded-2xl shadow-lg">
                <XCircle className="w-8 h-8 text-white" />
              </div>
              <h2 className="text-3xl sm:text-4xl font-bold bg-gradient-to-r from-red-600 to-pink-600 bg-clip-text text-transparent">
                4. Prohibited Uses
              </h2>
            </div>
            <div className="text-gray-300 space-y-4 text-lg leading-relaxed">
              <p className="font-medium text-gray-200">
                You may not use these services to create or process content that is:
              </p>
              <div className="grid sm:grid-cols-2 gap-4">
                <div className="flex items-center gap-3 p-3 bg-red-900/20 rounded-xl border border-red-700">
                  <XCircle className="w-5 h-5 text-red-400 flex-shrink-0" />
                  <span>Illegal, harmful, or offensive</span>
                </div>
                <div className="flex items-center gap-3 p-3 bg-red-900/20 rounded-xl border border-red-700">
                  <XCircle className="w-5 h-5 text-red-400 flex-shrink-0" />
                  <span>Violates intellectual property rights</span>
                </div>
                <div className="flex items-center gap-3 p-3 bg-red-900/20 rounded-xl border border-red-700">
                  <XCircle className="w-5 h-5 text-red-400 flex-shrink-0" />
                  <span>Contains explicit, violent, or inappropriate content</span>
                </div>
                <div className="flex items-center gap-3 p-3 bg-red-900/20 rounded-xl border border-red-700">
                  <XCircle className="w-5 h-5 text-red-400 flex-shrink-0" />
                  <span>Promotes hate speech or discrimination</span>
                </div>
                <div className="flex items-center gap-3 p-3 bg-red-900/20 rounded-xl border border-red-700">
                  <XCircle className="w-5 h-5 text-red-400 flex-shrink-0" />
                  <span>Attempts to create deepfakes of real people without consent</span>
                </div>
                <div className="flex items-center gap-3 p-3 bg-red-900/20 rounded-xl border border-red-700">
                  <XCircle className="w-5 h-5 text-red-400 flex-shrink-0" />
                  <span>For creating violent content</span>
                </div>
              </div>
            </div>
          </section>

          {/* Advertising and Monetization */}
          <section className="bg-gradient-to-br from-gray-800 via-emerald-900/20 to-gray-800 rounded-3xl shadow-2xl p-8 sm:p-12 border border-emerald-800 animate-fade-in-up">
            <div className="flex items-center gap-4 mb-6">
              <div className="p-3 bg-gradient-to-r from-emerald-500 to-green-500 rounded-2xl shadow-lg">
                <DollarSign className="w-8 h-8 text-white" />
              </div>
              <h2 className="text-3xl sm:text-4xl font-bold bg-gradient-to-r from-emerald-600 to-green-600 bg-clip-text text-transparent">
                5. Advertising and Monetization
              </h2>
            </div>
            <div className="text-gray-300 space-y-6 text-lg leading-relaxed">
              <p className="font-medium text-gray-200">
                To keep our service free and maintain operational costs, we display third-party advertisements on our website in future:
              </p>

              <div className="grid sm:grid-cols-2 gap-4">
                <div className="flex items-start gap-3 p-4 bg-emerald-900/20 rounded-xl border border-emerald-700">
                  <Info className="w-5 h-5 text-emerald-400 flex-shrink-0 mt-0.5" />
                  <span>We may display advertisements from third-party advertising networks</span>
                </div>
                <div className="flex items-start gap-3 p-4 bg-emerald-900/20 rounded-xl border border-emerald-700">
                  <Info className="w-5 h-5 text-emerald-400 flex-shrink-0 mt-0.5" />
                  <span>Advertisements will help us maintain free service</span>
                </div>
                <div className="flex items-start gap-3 p-4 bg-emerald-900/20 rounded-xl border border-emerald-700">
                  <Info className="w-5 h-5 text-emerald-400 flex-shrink-0 mt-0.5" />
                  <span>We do not control the content of third-party advertisements</span>
                </div>
                <div className="flex items-start gap-3 p-4 bg-emerald-900/20 rounded-xl border border-emerald-700">
                  <Info className="w-5 h-5 text-emerald-400 flex-shrink-0 mt-0.5" />
                  <span>Clicking on advertisements may redirect you to external websites</span>
                </div>
                <div className="flex items-start gap-3 p-4 bg-emerald-900/20 rounded-xl border border-emerald-700">
                  <Info className="w-5 h-5 text-emerald-400 flex-shrink-0 mt-0.5" />
                  <span>We are not responsible for the content or practices of advertised websites</span>
                </div>
                <div className="flex items-start gap-3 p-4 bg-emerald-900/20 rounded-xl border border-emerald-700">
                  <Info className="w-5 h-5 text-emerald-400 flex-shrink-0 mt-0.5" />
                  <span>Advertisement will revenue helps us cover server costs and service improvements</span>
                </div>
              </div>

              <div className="bg-gradient-to-r from-emerald-900/30 to-green-900/30 rounded-2xl p-6 border border-emerald-700">
                <p className="text-emerald-200 font-medium text-center">
                  By using our service, you acknowledge and accept the presence of advertisements as part of our business model to provide free AI tools.
                </p>
              </div>
            </div>
          </section>

          {/* Intellectual Property */}
          <section className="bg-gradient-to-br from-gray-800 via-purple-900/20 to-gray-800 rounded-3xl shadow-2xl p-8 sm:p-12 border border-purple-800 animate-fade-in-up">
            <div className="flex items-center gap-4 mb-6">
              <div className="p-3 bg-gradient-to-r from-purple-500 to-indigo-500 rounded-2xl shadow-lg">
                <Shield className="w-8 h-8 text-white" />
              </div>
              <h2 className="text-3xl sm:text-4xl font-bold bg-gradient-to-r from-purple-600 to-indigo-600 bg-clip-text text-transparent">
                6. Intellectual Property
              </h2>
            </div>
            <div className="text-gray-300 space-y-6 text-lg leading-relaxed">
              <p className="font-medium text-gray-200">
                Content processed through our AI tools is created using AI technology. While you may use the processed content, please note:
              </p>

              <div className="grid sm:grid-cols-2 gap-4">
                <div className="flex items-start gap-3 p-4 bg-purple-900/20 rounded-xl border border-purple-700">
                  <Eye className="w-5 h-5 text-purple-400 flex-shrink-0 mt-0.5" />
                  <span>The AI model may have been trained on copyrighted material</span>
                </div>
                <div className="flex items-start gap-3 p-4 bg-purple-900/20 rounded-xl border border-purple-700">
                  <Eye className="w-5 h-5 text-purple-400 flex-shrink-0 mt-0.5" />
                  <span>AI-processed content may resemble existing copyrighted works</span>
                </div>
                <div className="flex items-start gap-3 p-4 bg-purple-900/20 rounded-xl border border-purple-700">
                  <Eye className="w-5 h-5 text-purple-400 flex-shrink-0 mt-0.5" />
                  <span>You are responsible for ensuring your use complies with applicable laws</span>
                </div>
                <div className="flex items-start gap-3 p-4 bg-purple-900/20 rounded-xl border border-purple-700">
                  <Eye className="w-5 h-5 text-purple-400 flex-shrink-0 mt-0.5" />
                  <span>We do not claim ownership of your processed content</span>
                </div>
              </div>

              <div className="bg-gradient-to-r from-purple-900/30 to-indigo-900/30 rounded-2xl p-6 border border-purple-700">
                <p className="text-purple-200 font-medium text-center">
                  We reserve the right to use processed content for service improvement purposes only.
                </p>
              </div>
            </div>
          </section>

          {/* Privacy and Data */}
          <section className="bg-gradient-to-br from-gray-800 via-cyan-900/20 to-gray-800 rounded-3xl shadow-2xl p-8 sm:p-12 border border-cyan-800 animate-fade-in-up">
            <div className="flex items-center gap-4 mb-6">
              <div className="p-3 bg-gradient-to-r from-cyan-500 to-blue-500 rounded-2xl shadow-lg">
                <Lock className="w-8 h-8 text-white" />
              </div>
              <h2 className="text-3xl sm:text-4xl font-bold bg-gradient-to-r from-cyan-600 to-blue-600 bg-clip-text text-transparent">
                7. Privacy and Data
              </h2>
            </div>
            <div className="text-gray-300 space-y-6 text-lg leading-relaxed">
              <p className="font-medium text-gray-200">
                We respect your privacy and handle your data responsibly:
              </p>

              <div className="grid sm:grid-cols-2 gap-4">
                <div className="flex items-start gap-3 p-4 bg-cyan-900/20 rounded-xl border border-cyan-700">
                  <Lock className="w-5 h-5 text-cyan-400 flex-shrink-0 mt-0.5" />
                  <span>Input data is processed through AI models. We do not store any data.</span>
                </div>
                <div className="flex items-start gap-3 p-4 bg-cyan-900/20 rounded-xl border border-cyan-700">
                  <Lock className="w-5 h-5 text-cyan-400 flex-shrink-0 mt-0.5" />
                  <span>The content you create won't be saved in the cloud or shared with others.</span>
                </div>
                <div className="flex items-start gap-3 p-4 bg-cyan-900/20 rounded-xl border border-cyan-700">
                  <Lock className="w-5 h-5 text-cyan-400 flex-shrink-0 mt-0.5" />
                  <span>We do not store personal information.</span>
                </div>
                <div className="flex items-start gap-3 p-4 bg-cyan-900/20 rounded-xl border border-cyan-700">
                  <Lock className="w-5 h-5 text-cyan-400 flex-shrink-0 mt-0.5" />
                  <span>Unlike other AI creation tools, we do not publish your work.</span>
                </div>
              </div>

              <div className="bg-gradient-to-r from-cyan-900/30 to-blue-900/30 rounded-2xl p-6 border border-cyan-700">
                <p className="text-cyan-200 font-medium text-center">
                 We do not use your information.
                </p>
              </div>
            </div>
          </section>

          {/* Disclaimer */}
          <section className="bg-gradient-to-br from-gray-800 via-orange-900/20 to-gray-800 rounded-3xl shadow-2xl p-8 sm:p-12 border border-orange-800 animate-fade-in-up">
            <div className="flex items-center gap-4 mb-6">
              <div className="p-3 bg-gradient-to-r from-orange-500 to-red-500 rounded-2xl shadow-lg">
                <Info className="w-8 h-8 text-white" />
              </div>
              <h2 className="text-3xl sm:text-4xl font-bold bg-gradient-to-r from-orange-600 to-red-600 bg-clip-text text-transparent">
                8. Disclaimer
              </h2>
            </div>
            <div className="text-gray-300 space-y-6 text-lg leading-relaxed">
              <p className="font-medium text-gray-200">
                This service is provided "as is" without any warranties:
              </p>

              <div className="grid sm:grid-cols-2 gap-4">
                <div className="flex items-start gap-3 p-4 bg-orange-900/20 rounded-xl border border-orange-700">
                  <Info className="w-5 h-5 text-orange-400 flex-shrink-0 mt-0.5" />
                  <span>We do not guarantee the accuracy or quality of processed content</span>
                </div>
                <div className="flex items-start gap-3 p-4 bg-orange-900/20 rounded-xl border border-orange-700">
                  <Info className="w-5 h-5 text-orange-400 flex-shrink-0 mt-0.5" />
                  <span>The service may be unavailable due to maintenance or technical issues</span>
                </div>
                <div className="flex items-start gap-3 p-4 bg-orange-900/20 rounded-xl border border-orange-700">
                  <Info className="w-5 h-5 text-orange-400 flex-shrink-0 mt-0.5" />
                  <span>Processed results may not match your expectations</span>
                </div>
                <div className="flex items-start gap-3 p-4 bg-orange-900/20 rounded-xl border border-orange-700">
                  <Info className="w-5 h-5 text-orange-400 flex-shrink-0 mt-0.5" />
                  <span>We are not liable for any damages resulting from use of this service</span>
                </div>
              </div>
            </div>
          </section>

          {/* Limitation of Liability */}
          <section className="bg-gradient-to-br from-gray-800 via-gray-700 to-gray-800 rounded-3xl shadow-2xl p-8 sm:p-12 border border-gray-600 animate-fade-in-up">
            <div className="flex items-center gap-4 mb-6">
              <div className="p-3 bg-gradient-to-r from-gray-500 to-gray-600 rounded-2xl shadow-lg">
                <Scale className="w-8 h-8 text-white" />
              </div>
              <h2 className="text-3xl sm:text-4xl font-bold bg-gradient-to-r from-gray-600 to-gray-700 bg-clip-text text-transparent">
                9. Limitation of Liability
              </h2>
            </div>
            <div className="text-gray-300 space-y-4 text-lg leading-relaxed">
              <div className="bg-gray-700 rounded-2xl p-6 border border-gray-600">
                <p className="font-medium text-gray-200">
                  In no event shall we be liable for any indirect, incidental, special, consequential, or punitive damages, including without limitation, loss of profits, data, use, goodwill, or other intangible losses.
                </p>
              </div>
            </div>
          </section>

          {/* Changes to Terms */}
          <section className="bg-gradient-to-br from-gray-800 via-indigo-900/20 to-gray-800 rounded-3xl shadow-2xl p-8 sm:p-12 border border-indigo-800 animate-fade-in-up">
            <div className="flex items-center gap-4 mb-6">
              <div className="p-3 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-2xl shadow-lg">
                <FileText className="w-8 h-8 text-white" />
              </div>
              <h2 className="text-3xl sm:text-4xl font-bold bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent">
                10. Changes to Terms
              </h2>
            </div>
            <div className="text-gray-300 space-y-4 text-lg leading-relaxed">
              <div className="bg-indigo-900/30 rounded-2xl p-6 border border-indigo-700">
                <p className="font-medium text-indigo-200">
                  We reserve the right to modify these terms at any time. Changes will be effective immediately upon posting. Your continued use of the service constitutes acceptance of the modified terms.
                </p>
              </div>
            </div>
          </section>

          {/* Contact */}
          <section className="bg-gradient-to-br from-gray-800 via-teal-900/20 to-gray-800 rounded-3xl shadow-2xl p-8 sm:p-12 border border-teal-800 animate-fade-in-up">
            <div className="flex items-center gap-4 mb-6">
              <div className="p-3 bg-gradient-to-r from-teal-500 to-cyan-500 rounded-2xl shadow-lg">
                <Mail className="w-8 h-8 text-white" />
              </div>
              <h2 className="text-3xl sm:text-4xl font-bold bg-gradient-to-r from-teal-600 to-cyan-600 bg-clip-text text-transparent">
                11. Contact Information
              </h2>
            </div>
            <div className="text-gray-300 space-y-4 text-lg leading-relaxed">
              <div className="bg-teal-900/30 rounded-2xl p-6 border border-teal-700">
                <p className="font-medium text-teal-200 text-center">
                  If you have any questions about these Terms and Conditions, please contact us at{' '}
                  <a href="mailto:<EMAIL>" className="underline hover:no-underline">
                    <EMAIL>
                  </a>
                </p>
              </div>
            </div>
          </section>

          {/* Enhanced Final Agreement */}
          <div className="mt-12 bg-gradient-to-br from-blue-900/20 via-purple-800/10 to-blue-900/20 border-2 border-blue-800 rounded-3xl p-8 sm:p-12 shadow-2xl animate-fade-in-up">
            <div className="text-center">
              <div className="flex justify-center mb-6">
                <div className="p-4 bg-gradient-to-r from-blue-500 to-purple-500 rounded-2xl shadow-lg">
                  <CheckCircle className="w-10 h-10 text-white" />
                </div>
              </div>

              <h3 className="text-2xl sm:text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-6">
                Agreement Acknowledgment
              </h3>

              <div className="bg-gray-800 rounded-2xl p-6 border border-blue-700 mb-6">
                <p className="text-lg font-medium text-gray-200 leading-relaxed">
                  By using these AI tools, you acknowledge that you have read, understood, and agree to be bound by these Terms and Conditions.
                </p>
              </div>

              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link
                  to="/"
                  className="inline-flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-medium rounded-xl transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105"
                >
                  <CheckCircle className="w-5 h-5" />
                  I Accept - Back to Home
                </Link>

                <button
                  onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}
                  className="inline-flex items-center gap-2 px-6 py-3 bg-gray-800 hover:bg-gray-700 text-gray-300 border-2 border-gray-600 rounded-xl transition-all duration-200 font-medium shadow-lg hover:shadow-xl transform hover:scale-105"
                >
                  <ArrowLeft className="w-5 h-5" />
                  Back to Top
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Terms;
