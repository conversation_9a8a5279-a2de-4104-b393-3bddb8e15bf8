import React, { useState, useRef, useEffect } from 'react';
import {
  Upload,
  Link as LinkIcon,
  History,
  Sparkles,
  X,
  Copy,
  Check,
  Wand2,
  Image as ImageIcon,
  Languages,
  FileText,
  Loader2,
  Zap,
  AlertCircle,
  ChevronDown,
  Eye,
  Palette,
  Globe,
  Download,
  Shield,
  ChevronUp,
  MessageCircle
} from 'lucide-react';
import axios from 'axios';
import SEO, { pageSEO } from '../components/SEO';

// Custom Select Component for consistent UI
const CustomSelect = ({ label, value, options, onChange }) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef(null);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  return (
    <div className="space-y-2" ref={dropdownRef}>
      <label className="text-base text-gray-400 font-semibold ml-1">{label}</label>
      <div className="relative">
        <button
          onClick={() => setIsOpen(!isOpen)}
          className={`w-full text-left bg-[#13131a] border ${isOpen ? 'border-purple-500' : 'border-gray-700'} hover:border-gray-600 text-white text-lg rounded-xl px-5 py-4 flex items-center justify-between transition-all duration-200 outline-none focus:ring-1 focus:ring-purple-500/50`}
        >
          <span className="truncate font-medium">{value}</span>
          <ChevronDown
            size={20}
            className={`text-gray-500 transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`}
          />
        </button>

        {isOpen && (
          <div className="absolute top-full left-0 right-0 mt-2 bg-[#1a1a23] border border-gray-700 rounded-xl shadow-2xl z-50 overflow-hidden animate-in fade-in zoom-in-95 duration-100">
            <div className="max-h-60 overflow-y-auto custom-scrollbar p-1">
              {options.map((option) => (
                <div
                  key={option}
                  onClick={() => {
                    onChange(option);
                    setIsOpen(false);
                  }}
                  className={`px-4 py-3 text-lg cursor-pointer rounded-lg flex items-center justify-between transition-colors ${
                    value === option
                      ? 'bg-purple-900/30 text-purple-400 font-semibold'
                      : 'text-gray-300 hover:bg-[#252530] hover:text-white'
                  }`}
                >
                  <span className="truncate font-medium">{option}</span>
                  {value === option && <Check size={16} className="text-purple-400 flex-shrink-0 ml-2" />}
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

// Feature Card Component
const FeatureCard = ({ icon: Icon, color, title, description }) => (
  <div className="bg-[#1a1a23] border border-gray-800 p-8 rounded-3xl hover:bg-[#1f1f2a] hover:border-gray-700 transition-all duration-300 group min-h-[280px]">
    <div className={`w-16 h-16 rounded-xl flex items-center justify-center mb-6 bg-[#13131a] group-hover:scale-110 transition-transform duration-300`}>
      <Icon className={`w-8 h-8 ${color}`} />
    </div>
    <h3 className="text-2xl font-bold text-white mb-4">{title}</h3>
    <p className="text-gray-400 text-lg leading-relaxed">{description}</p>
  </div>
);

// FAQ Item Component
const FAQItem = ({ question, answer, isOpen, onClick }) => (
  <div className="border border-gray-800 rounded-3xl overflow-hidden mb-6 bg-[#1a1a23]/30">
    <button
      className="w-full px-10 py-8 text-left flex items-center justify-between hover:bg-[#1a1a23]/50 transition-colors"
      onClick={onClick}
    >
      <span className="text-gray-200 font-bold text-xl">{question}</span>
      {isOpen ? (
        <ChevronUp className="w-7 h-7 text-purple-400 flex-shrink-0 ml-8" />
      ) : (
        <ChevronDown className="w-7 h-7 text-gray-500 flex-shrink-0 ml-8" />
      )}
    </button>
    <div
      className={`transition-all duration-300 ease-in-out overflow-hidden ${
        isOpen ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'
      }`}
    >
      <div className="px-10 pb-8 pt-0 text-gray-400 text-lg leading-relaxed">
        {answer}
      </div>
    </div>
  </div>
);

const ImageToPrompt = () => {
  const [selectedImage, setSelectedImage] = useState(null);
  const [imageFile, setImageFile] = useState(null);
  const [generatedPrompt, setGeneratedPrompt] = useState('');
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [copySuccess, setCopySuccess] = useState(false);
  const [toast, setToast] = useState({ show: false, message: '', type: 'success' });
  
  // Configuration States
  const [aiModel, setAiModel] = useState('General Image Prompt');
  const [language, setLanguage] = useState('English');
  const [format, setFormat] = useState('Text (TXT)');

  // FAQ State
  const [openFAQ, setOpenFAQ] = useState(0);

  const fileInputRef = useRef(null);

  // Options matching the video
  const aiModels = [
    'General Image Prompt',
    'Flux',
    'Nano Banana',
    'Midjourney',
    'Stable Diffusion',
    'DALL-E',
    'Seedream 4',
    'Qwen',
    'GPT-4o mini'
  ];

  const languages = [
    'English', 'Spanish', 'French', 'German', 'Italian', 
    'Portuguese', 'Chinese', 'Japanese', 'Korean', 'Russian', 'Turkish', 'Arabic'
  ];

  const formats = ['Text (TXT)', 'JSON'];

  // Features data
  const features = [
    {
      icon: Eye,
      color: "text-cyan-400",
      title: "Detailed Visual Analysis",
      description: "Analyzes objects, scenes, composition, perspective, lighting, and technical details to create specific, usable prompts."
    },
    {
      icon: Palette,
      color: "text-purple-400",
      title: "Style & Mood Detection",
      description: "Recognizes artistic styles, visual aesthetics, color palettes, and mood to ensure consistent results across generations."
    },
    {
      icon: Sparkles,
      color: "text-pink-400",
      title: "Model-Friendly Formatting",
      description: "Instantly usable prompts tailored for Midjourney, DALL-E, Stable Diffusion, Leonardo, and more."
    },
    {
      icon: Globe,
      color: "text-green-400",
      title: "Multi-Language Output",
      description: "Generate prompts in any language that fits your workflow and reaches your audience."
    },
    {
      icon: Download,
      color: "text-orange-400",
      title: "One-Click Copy & Download",
      description: "Instantly copy prompts to your clipboard or download them for easy reuse and sharing."
    },
    {
      icon: Shield,
      color: "text-red-400",
      title: "Privacy-Friendly",
      description: "Designed with privacy in mind. We never permanently store your images, and processing uses secure, temporary uploads."
    }
  ];

  const showToast = (message, type = 'success') => {
    setToast({ show: true, message, type });
    setTimeout(() => setToast({ show: false, message: '', type: 'success' }), 3000);
  };

  // FAQs data
  const faqs = [
    {
      question: "How does the Image to Prompt AI work?",
      answer: "Our advanced AI analyzes your uploaded image using computer vision technology to identify objects, scenes, styles, and artistic elements. It then generates detailed, professional prompts that can be used with AI art generators like Midjourney, DALL-E, or Stable Diffusion to recreate similar images."
    },
    {
      question: "What types of images work best for prompt generation?",
      answer: "Our AI works with all types of images - photographs, artwork, illustrations, digital art, and more. High-quality images with clear subjects and good lighting typically produce the most detailed prompts, but our AI can analyze any image format including PNG, JPG, JPEG, and WEBP."
    },
    {
      question: "Is my image data secure and private?",
      answer: "Absolutely! All image processing happens securely. Your images never leave our secure processing environment and are deleted immediately after analysis. We ensure complete privacy and security of your files."
    },
    {
      question: "Can I edit or customize the generated prompts?",
      answer: "Yes! Once the AI generates your prompt, you can easily copy it and modify it to suit your needs. Add specific styles, adjust descriptions, or combine multiple prompts to create exactly what you're looking for."
    },
    {
      question: "What AI art platforms work with these prompts?",
      answer: "Our generated prompts are compatible with all major AI art platforms including Midjourney, DALL-E 2/3, Stable Diffusion, Adobe Firefly, and many others. The prompts are formatted to work universally across different AI image generators."
    },
    {
      question: "Are there any file size or format limitations?",
      answer: "We support all common image formats (PNG, JPG, JPEG, WEBP) with files up to 10MB. There's no limit on image dimensions, and our AI can process everything from small icons to high-resolution photographs."
    },
    {
      question: "How accurate are the generated prompts?",
      answer: "Our AI uses state-of-the-art computer vision models trained on millions of images to provide highly accurate descriptions. While results may vary based on image complexity, most users find the generated prompts capture 90%+ of the important visual elements."
    },
    {
      question: "Do I need to create an account to use this tool?",
      answer: "No account required! Our Image to Prompt tool is completely free to use without registration. Simply upload your image and get instant results."
    }
  ];

  const handleFileChange = (event) => {
    const file = event.target.files[0];
    if (file) {
      processFile(file);
    }
  };

  const processFile = (file) => {
    // 1. Validate File Size (Max 10MB)
    if (file.size > 10 * 1024 * 1024) {
      showToast("File size exceeds 10MB limit.", "error");
      return;
    }

    // 2. Validate File Type (JPG, JPEG, PNG, WEBP)
    const validTypes = ['image/jpeg', 'image/png', 'image/webp', 'image/jpg'];
    if (!validTypes.includes(file.type)) {
      showToast("Invalid file format. Please upload JPG, PNG, JPEG, or WEBP images only.", "error");
      return;
    }

    setImageFile(file);
    const reader = new FileReader();
    reader.onloadend = () => {
      setSelectedImage(reader.result);
      setGeneratedPrompt(''); // Clear previous results
    };
    reader.readAsDataURL(file);
  };

  const handleDragOver = (e) => {
    e.preventDefault();
  };

  const handleDrop = (e) => {
    e.preventDefault();
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      processFile(e.dataTransfer.files[0]);
    }
  };

  const removeImage = (e) => {
    e.stopPropagation();
    setSelectedImage(null);
    setImageFile(null);
    if (fileInputRef.current) fileInputRef.current.value = '';
  };

  const copyToClipboard = () => {
    navigator.clipboard.writeText(generatedPrompt);
    setCopySuccess(true);
    setTimeout(() => setCopySuccess(false), 2000);
    showToast("Prompt copied to clipboard!", "success");
  };

  const generatePrompt = async () => {
    if (!selectedImage) return;

    setIsAnalyzing(true);
    setGeneratedPrompt('');

    try {
      // Convert image to base64 for API
      const base64data = selectedImage;

      const response = await axios.post(`${import.meta.env.VITE_API_BASE_URL}/api/ai/image-to-prompt`, {
        imageData: base64data,
        model: aiModel,
        language: language,
        format: format
      });

      if (response.data.success) {
        setGeneratedPrompt(response.data.prompt);
        showToast("Prompt generated successfully!", "success");
      } else {
        throw new Error(response.data.error || 'Failed to generate prompt');
      }
    } catch (error) {
      console.error('API Error:', error);
      showToast(error.response?.data?.error || 'Failed to generate prompt', "error");
    } finally {
      setIsAnalyzing(false);
    }
  };

  const isButtonDisabled = isAnalyzing || !selectedImage;

  return (
    <div className="min-h-screen bg-[#0f0f13] text-gray-200 font-sans p-4 md:p-8 flex flex-col items-center relative justify-center">
      <SEO {...pageSEO.imageToPrompt} />

      {/* Toast Notification */}
      {toast.show && (
        <div className={`fixed top-6 left-1/2 transform -translate-x-1/2 z-50 flex items-center gap-3 px-6 py-3 rounded-xl shadow-2xl transition-all duration-300 animate-in fade-in slide-in-from-top-4 ${
          toast.type === 'success'
            ? 'bg-[#0f291e] border border-green-500/50 text-green-400'
            : 'bg-[#290f0f] border border-red-500/50 text-red-400'
        }`}>
          <div className={`p-1 rounded-full ${toast.type === 'success' ? 'bg-green-500/20' : 'bg-red-500/20'}`}>
            {toast.type === 'success' ? <Check size={16} /> : <AlertCircle size={16} />}
          </div>
          <span className="text-sm font-semibold">{toast.message}</span>
          <button onClick={() => setToast({ ...toast, show: false })} className="ml-2 hover:opacity-70">
            <X size={14} />
          </button>
        </div>
      )}

      {/* Header Section */}
      <div className="text-center max-w-4xl mx-auto px-4 pb-16">
        <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-6">
          AI Image to <span className="text-[#A7D6F2]">Prompt Generator</span>
        </h1>
        <p className="text-lg md:text-xl text-gray-400 leading-relaxed max-w-3xl mx-auto">
          Upload an image to generate a professional, detailed prompt that describes its content, style, composition, and lighting. Copy it and use it with your favorite AI image model.
        </p>
      </div>

      {/* Main Generator Section */}
      <div className="w-full max-w-7xl grid grid-cols-1 lg:grid-cols-2 gap-8">

        {/* LEFT PANEL - INPUT */}
        {/* Removed overflow-hidden to allow CustomSelect dropdowns to float outside */}
        <div className="bg-[#1a1a23] border border-gray-800 rounded-3xl p-8 flex flex-col h-full shadow-2xl relative group min-h-[700px]">

            <div className="flex items-center gap-4 mb-8">
                <div className="p-3 bg-purple-900/30 rounded-xl text-purple-400">
                    <Wand2 size={28} />
                </div>
                <h2 className="text-2xl font-bold text-white">AI Image to Prompt Generator</h2>
            </div>

            {/* Main Input Area */}
            <div className="flex-grow flex flex-col">
                <div className="mb-5">
                    <label className="text-xl text-gray-400 font-medium mb-2 flex items-center gap-2">
                            <ImageIcon size={14} /> Source Image for Prompt Generation {selectedImage ? '1/1' : '0/1'}
                    </label>

                    {!selectedImage ? (
                        <div
                            onClick={() => fileInputRef.current?.click()}
                            onDragOver={handleDragOver}
                            onDrop={handleDrop}
                            className="border-2 border-dashed border-gray-700 bg-[#15151e] rounded-2xl h-64 flex flex-col items-center justify-center cursor-pointer hover:border-purple-500/50 hover:bg-[#1a1a25] transition-all duration-300 group/drop"
                        >
                            <div className="w-22 h-22 bg-[#2a2a35] rounded-full flex items-center justify-center mb-4 group-hover/drop:scale-110 transition-transform">
                                <span className="text-5xl text-gray-400">+</span>
                            </div>
                            <p className="text-gray-300 font-medium mb-1 text-xl">Upload Image for Prompt</p>
                            <p className="text-gray-300 font-medium">Generation</p>
                            <p className="text-xs text-gray-500 mt-2">Max 10MB (JPG, PNG, WEBP)</p>
                            <input
                                type="file"
                                ref={fileInputRef}
                                className="hidden"
                                accept=".jpg,.jpeg,.png,.webp"
                                onChange={handleFileChange}
                            />
                        </div>
                    ) : (
                        <div className="relative rounded-2xl overflow-hidden border border-purple-500/30 bg-[#15151e] group/img h-64 flex items-center justify-center">
                            <img src={selectedImage} alt="Uploaded" className="max-h-full max-w-full object-contain" />
                            <div className="absolute inset-0 bg-black/40 opacity-0 group-hover/img:opacity-100 transition-opacity flex items-center justify-center">
                                <button
                                    onClick={removeImage}
                                    className="bg-red-500/80 hover:bg-red-600 text-white p-2 rounded-full transform hover:scale-110 transition-all"
                                >
                                    <X size={20} />
                                </button>
                            </div>
                            <div className="absolute bottom-2 left-2 bg-black/70 px-2 py-1 rounded text-xs text-white">
                                {(imageFile.size / (1024 * 1024)).toFixed(2)} MB
                            </div>
                        </div>
                    )}
                </div>

                {/* Settings Grid using CustomSelect */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                    <CustomSelect
                        label="AI Model"
                        value={aiModel}
                        options={aiModels}
                        onChange={setAiModel}
                    />

                    <CustomSelect
                        label="Prompt Language"
                        value={language}
                        options={languages}
                        onChange={setLanguage}
                    />

                    <CustomSelect
                        label="Prompt Format"
                        value={format}
                        options={formats}
                        onChange={setFormat}
                    />
                </div>

                {/* Footer Actions */}
                <div className="mt-auto flex flex-col md:flex-row items-center justify-between gap-6 pt-6 border-t border-gray-800">
                    <div className="flex-1"></div> {/* Spacer to push button to right */}
                    <button
                        onClick={generatePrompt}
                        disabled={isButtonDisabled}
                        className={`w-full md:w-auto px-10 py-4 rounded-xl font-bold text-white flex items-center justify-center gap-3 transition-all duration-300 shadow-lg shadow-purple-900/20 text-lg
                            ${isButtonDisabled
                                ? 'bg-gray-700 cursor-not-allowed opacity-50'
                                : 'bg-[#7E22CE] hover:bg-[#6b1cb0] transform hover:-translate-y-0.5'}`}
                    >
                        {isAnalyzing ? (
                            <><Loader2 className="animate-spin" size={20}/> Analyzing...</>
                        ) : (
                            <><Sparkles size={18} fill="currentColor" /> Generate Prompt</>
                        )}
                    </button>
                </div>
            </div>
        </div>

        {/* RIGHT PANEL - OUTPUT */}
        <div className="bg-[#1a1a23] border border-gray-800 rounded-3xl p-8 flex flex-col h-full min-h-[700px] shadow-2xl relative overflow-hidden">
             {/* Header */}
             <div className="flex items-center gap-4 mb-8">
                <div className="p-3 bg-purple-900/30 rounded-xl text-purple-400">
                    <FileText size={28} />
                </div>
                <div>
                     <h2 className="text-2xl font-bold text-white">Generated Prompts</h2>
                     <p className="text-xl text-gray-400">Your AI-generated prompts appear here instantly</p>
                </div>
            </div>

            {/* Content Area */}
            <div className="flex-grow flex flex-col relative min-h-0">

                {/* Empty State */}
                {!isAnalyzing && !generatedPrompt && (
                    <div className="absolute inset-0 flex flex-col items-center justify-center text-center p-10">
                        <div className="w-24 h-24 bg-gradient-to-br from-purple-600 to-blue-600 rounded-3xl flex items-center justify-center mb-8 shadow-xl shadow-purple-900/30 transform -rotate-3">
                            <Wand2 size={48} className="text-white" />
                        </div>
                        <h3 className="text-3xl font-bold text-white mb-4">Ready for prompt generation</h3>
                        <p className="text-gray-400 max-w-sm text-lg">
                            Upload your image and get detailed prompts optimized for {aiModel}.
                        </p>
                    </div>
                )}

                {/* Loading State */}
                {isAnalyzing && (
                     <div className="absolute inset-0 flex flex-col items-center justify-center text-center p-10 z-10 bg-[#1a1a23]/80 backdrop-blur-sm">
                        <div className="relative mb-8">
                             <div className="w-24 h-24 rounded-full border-4 border-purple-900/30 border-t-purple-500 animate-spin"></div>
                             <div className="absolute inset-0 flex items-center justify-center">
                                 <Zap size={28} className="text-purple-400 animate-pulse" />
                             </div>
                        </div>
                        <h3 className="text-2xl font-bold text-white mb-4 animate-pulse">AI Analyzing...</h3>
                        <p className="text-gray-400 text-base">Please wait while AI analyzes your image structure, colors, and composition.</p>
                     </div>
                )}

                {/* Result State */}
                {!isAnalyzing && generatedPrompt && (
                    <div className="absolute inset-0 flex flex-col animate-in fade-in duration-500">
                        <div className="flex-grow bg-[#13131a] rounded-2xl p-6 border border-gray-700/50 overflow-auto mb-6 font-mono text-base text-gray-300 leading-relaxed custom-scrollbar">
                            {format === 'JSON' ? (
                                <pre className="whitespace-pre-wrap">{generatedPrompt}</pre>
                            ) : (
                                <p className="whitespace-pre-wrap">{generatedPrompt}</p>
                            )}
                        </div>

                        <div className="flex items-center justify-between gap-4">
                            <div className="flex gap-3">
                                <span className="px-4 py-2 bg-purple-900/20 text-purple-400 text-sm rounded-full border border-purple-500/20 font-medium">
                                    {aiModel}
                                </span>
                                <span className="px-4 py-2 bg-gray-800 text-gray-400 text-sm rounded-full border border-gray-700 font-medium">
                                    {language}
                                </span>
                            </div>

                            <button
                                onClick={copyToClipboard}
                                className={`px-6 py-3 rounded-xl flex items-center gap-3 text-base font-semibold transition-all ${
                                    copySuccess
                                    ? 'bg-green-500/20 text-green-400 border border-green-500/50'
                                    : 'bg-[#2a2a35] text-white hover:bg-[#353545] border border-gray-700'
                                }`}
                            >
                                {copySuccess ? <Check size={18} /> : <Copy size={18} />}
                                {copySuccess ? 'Copied!' : 'Copy Prompt'}
                            </button>
                        </div>
                    </div>
                )}
            </div>
        </div>
      </div>

      {/* What is Image to Prompt Section */}
      <div className="max-w-7xl mx-auto px-4 py-20 sm:px-6 lg:px-8">

        {/* Header Section */}
        <div className="text-center max-w-4xl mx-auto mb-20">
          <h1 className="text-5xl md:text-6xl lg:text-7xl font-bold text-white mb-8">
            What is <span className="text-purple-400 inline-block relative">
              Image to Prompt?
              <span className="absolute bottom-0 left-0 w-full h-2 bg-purple-500/20 rounded-full"></span>
            </span>
          </h1>
          <p className="text-xl md:text-2xl text-gray-400 leading-relaxed">
            Image to Prompt turns any image into a detailed text prompt ready for the AI image generator. It analyzes the subject, scene, composition, lighting, color, mood, and artistic style to create accurate, high-quality prompts.
          </p>
        </div>

        {/* Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-32">
          {features.map((feature, index) => (
            <FeatureCard key={index} {...feature} />
          ))}
        </div>

        {/* FAQ Section */}
        <div className="max-w-5xl mx-auto mb-20">
          <div className="text-center mb-16">
            <h2 className="text-5xl md:text-6xl font-bold text-white mb-6">Frequently Asked Questions</h2>
            <p className="text-xl text-gray-400">Everything you need to know about Image to Prompt AI</p>
          </div>

          <div className="space-y-6">
            {faqs.map((faq, index) => (
              <FAQItem
                key={index}
                question={faq.question}
                answer={faq.answer}
                isOpen={openFAQ === index}
                onClick={() => setOpenFAQ(openFAQ === index ? -1 : index)}
              />
            ))}
          </div>
        </div>

        {/* CTA Section */}
        <div className="max-w-4xl mx-auto">
          <div className="bg-gradient-to-r from-[#1a1a23] to-purple-950/50 border border-purple-500/20 rounded-3xl p-8 md:p-12 text-center relative overflow-hidden">
            {/* Background decoration */}
            <div className="absolute top-0 left-0 w-full h-full bg-[radial-gradient(circle_at_50%_0%,rgba(147,51,234,0.15),transparent_50%)]"></div>

            <div className="relative z-10">
              <h2 className="text-2xl md:text-3xl font-bold text-white mb-4">Need More Help?</h2>
              <p className="text-gray-400 mb-8 max-w-lg mx-auto">
                Can't find the answer you're looking for? Our support team is ready to assist you with any questions about Image to Prompt AI.
              </p>
              <button className="bg-gray-200 hover:bg-white text-gray-900 font-semibold py-3 px-8 rounded-lg transition-colors duration-200 inline-flex items-center gap-2">
                <MessageCircle className="w-5 h-5" />
                Contact Support
              </button>
            </div>
          </div>
        </div>

      </div>

      <style>{`
        .custom-scrollbar::-webkit-scrollbar {
          width: 8px;
        }
        .custom-scrollbar::-webkit-scrollbar-track {
          background: #13131a;
          border-radius: 4px;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb {
          background: #333340;
          border-radius: 4px;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb:hover {
          background: #444455;
        }
      `}</style>
    </div>
  );
};

export default ImageToPrompt;
