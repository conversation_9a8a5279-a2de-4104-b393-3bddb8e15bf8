import React, { useState, useEffect, useRef } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { Wand2, FileText, Info, HelpCircle, BookOpen, Menu, X, Scissors, Zap, ChevronDown, Settings, Home, Palette } from 'lucide-react';

import Logo from './Logo';

const Navigation = () => {
  const location = useLocation();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isToolsDropdownOpen, setIsToolsDropdownOpen] = useState(false);
  const dropdownRef = useRef(null);

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  const closeMobileMenu = () => {
    setIsMobileMenuOpen(false);
  };
  

  const toolsItems = [
    {
      name: 'Background Remove',
      path: '/background-remove',
      icon: Scissors,
    },
    {
      name: 'Upscale Image',
      path: '/upscale-image',
      icon: Zap,
    },

    {
      name: 'Image to Prompt',
      path: '/image-to-prompt',
      icon: Wand2,
    },
    {
      name: 'Logo Prompt',
      path: '/logo-prompt',
      icon: Palette,
    },
    {
      name: 'Image Prompt Generator',
      path: '/image-prompt-generator',
      icon: Wand2,
    },
  ];

  const navItems = [
    {
      name: 'Home',
      path: '/',
      icon: Home,
    },
    {
      name: 'Nano Banana Prompt',
      path: '/nano-banana-prompt',
      icon: Zap,
    },
    {
      name: 'Blog',
      path: '/blog',
      icon: BookOpen,
    },
    {
      name: 'About',
      path: '/about',
      icon: Info,
    },
    {
      name: 'FAQ',
      path: '/faq',
      icon: HelpCircle,
    },
    {
      name: 'Terms',
      path: '/terms',
      icon: FileText,
    },
  ];

  const isActive = (path) => {
    return location.pathname === path;
  };

  const isToolsActive = () => {
    return toolsItems.some(item => location.pathname === item.path);
  };

  const toggleToolsDropdown = () => {
    setIsToolsDropdownOpen(!isToolsDropdownOpen);
  };

  const closeDropdowns = () => {
    setIsToolsDropdownOpen(false);
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsToolsDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);



  return (
    <>
    <header className="sticky top-0 z-40 bg-gray-900/80 backdrop-blur-sm border-b border-gray-700 animate-slide-down">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <Link to="/" className="flex items-center space-x-3 group">
            <Logo size="medium" className="transition-transform duration-300 group-hover:scale-105" />

          </Link>

          {/* Navigation Links */}
          <nav className="hidden md:flex items-center space-x-1">
            {/* Home Link - First */}
            <Link
              to="/"
              onClick={closeDropdowns}
              className={`
                flex items-center gap-2 px-4 py-2 rounded-lg text-sm font-medium
                transition-all duration-200 hover:scale-105
                ${isActive('/')
                  ? 'bg-purple-900/30 text-purple-300 animate-scale-in'
                  : 'text-gray-300 hover:text-white hover:bg-gray-800'
                }
              `}
            >
              <Home className="w-4 h-4" />
              Home
            </Link>

            {/* Tools Dropdown - Second */}
            <div className="relative" ref={dropdownRef}>
              <button
                onClick={toggleToolsDropdown}
                className={`
                  flex items-center gap-2 px-4 py-2 rounded-lg text-sm font-medium
                  transition-all duration-200 hover:scale-105
                  ${isToolsActive()
                    ? 'bg-purple-900/30 text-purple-300 animate-scale-in'
                    : 'text-gray-300 hover:text-white hover:bg-gray-800'
                  }
                `}
              >
                <Settings className="w-4 h-4" />
                Tools
                <ChevronDown className={`w-4 h-4 transition-transform duration-200 ${isToolsDropdownOpen ? 'rotate-180' : ''}`} />
              </button>

              {/* Tools Dropdown Menu */}
              {isToolsDropdownOpen && (
                <div className="absolute top-full left-0 mt-2 w-96 bg-gray-900 border border-gray-700 rounded-lg shadow-xl z-50">
                  <div className="p-4">
                    <div className="grid grid-cols-1 gap-3">


                      {/* Background Remover */}
                      <Link
                        to="/background-remove"
                        onClick={closeDropdowns}
                        className="flex items-center gap-3 p-3 rounded-lg hover:bg-gray-800 transition-all duration-200 group"
                      >
                        <div className="w-12 h-12 rounded-lg overflow-hidden flex-shrink-0">
                          <img
                            src="https://ik.imagekit.io/q0mafimea/Before.png?updatedAt=1760793022106"
                            alt="Background Remover"
                            className="w-full h-full object-cover"
                          />
                        </div>
                        <div className="flex items-center gap-2 flex-1">
                          <div className="w-6 h-6 bg-cyan-500 rounded-full flex items-center justify-center flex-shrink-0">
                            <Scissors className="w-3 h-3 text-white" />
                          </div>
                          <div className="flex-1">
                            <div className="text-white font-medium text-sm group-hover:text-cyan-300 transition-colors">
                              Remove Background
                            </div>
                            <div className="text-gray-400 text-xs">
                              AI Automatic Cutout
                            </div>
                          </div>
                        </div>
                      </Link>

                      {/* Image Upscaler */}
                      <Link
                        to="/upscale-image"
                        onClick={closeDropdowns}
                        className="flex items-center gap-3 p-3 rounded-lg hover:bg-gray-800 transition-all duration-200 group"
                      >
                        <div className="w-12 h-12 rounded-lg overflow-hidden flex-shrink-0">
                          <img
                            src="https://ik.imagekit.io/q0mafimea/1.png?updatedAt=1760793055153"
                            alt="Image Upscaler"
                            className="w-full h-full object-cover"
                          />
                        </div>
                        <div className="flex items-center gap-2 flex-1">
                          <div className="w-6 h-6 bg-orange-500 rounded-full flex items-center justify-center flex-shrink-0">
                            <Zap className="w-3 h-3 text-white" />
                          </div>
                          <div className="flex-1">
                            <div className="text-white font-medium text-sm group-hover:text-orange-300 transition-colors">
                              Image Upscaler
                            </div>
                            <div className="text-gray-400 text-xs">
                              Without Quality Loss
                            </div>
                          </div>
                        </div>
                      </Link>



                      {/* Image to Prompt */}
                      <Link
                        to="/image-to-prompt"
                        onClick={closeDropdowns}
                        className="flex items-center gap-3 p-3 rounded-lg hover:bg-gray-800 transition-all duration-200 group"
                      >
                        <div className="w-12 h-12 rounded-lg overflow-hidden flex-shrink-0">
                          <img
                            src="https://ik.imagekit.io/q0mafimea/4.png?updatedAt=1760793053852"
                            alt="Image to Prompt"
                            className="w-full h-full object-cover"
                          />
                        </div>
                        <div className="flex items-center gap-2 flex-1">
                          <div className="w-6 h-6 bg-indigo-500 rounded-full flex items-center justify-center flex-shrink-0">
                            <Wand2 className="w-3 h-3 text-white" />
                          </div>
                          <div className="flex-1">
                            <div className="text-white font-medium text-sm group-hover:text-indigo-300 transition-colors">
                              Image to Prompt
                            </div>
                            <div className="text-gray-400 text-xs">
                              Extract Text From Image
                            </div>
                          </div>
                        </div>
                      </Link>

                      {/* Logo Prompt */}
                      <Link
                        to="/logo-prompt"
                        onClick={closeDropdowns}
                        className="flex items-center gap-3 p-3 rounded-lg hover:bg-gray-800 transition-all duration-200 group"
                      >
                        <div className="w-12 h-12 rounded-lg overflow-hidden flex-shrink-0">
                          <img
                            src="https://ik.imagekit.io/q0mafimea/5.png?updatedAt=1760793054123"
                            alt="Logo Prompt"
                            className="w-full h-full object-cover"
                          />
                        </div>
                        <div className="flex items-center gap-2 flex-1">
                          <div className="w-6 h-6 bg-pink-500 rounded-full flex items-center justify-center flex-shrink-0">
                            <Palette className="w-3 h-3 text-white" />
                          </div>
                          <div className="flex-1">
                            <div className="text-white font-medium text-sm group-hover:text-pink-300 transition-colors">
                              Logo Prompt
                            </div>
                            <div className="text-gray-400 text-xs">
                              Generate logo design prompts
                            </div>
                          </div>
                        </div>
                      </Link>

                      {/* Image Prompt Generator */}
                      <Link
                        to="/image-prompt-generator"
                        onClick={closeDropdowns}
                        className="flex items-center gap-3 p-3 rounded-lg hover:bg-gray-800 transition-all duration-200 group"
                      >
                        <div className="w-12 h-12 rounded-lg overflow-hidden flex-shrink-0">
                          <img
                            src="https://ik.imagekit.io/q0mafimea/6.png?updatedAt=1760793054456"
                            alt="Image Prompt Generator"
                            className="w-full h-full object-cover"
                          />
                        </div>
                        <div className="flex items-center gap-2 flex-1">
                          <div className="w-6 h-6 bg-yellow-500 rounded-full flex items-center justify-center flex-shrink-0">
                            <Wand2 className="w-3 h-3 text-white" />
                          </div>
                          <div className="flex-1">
                            <div className="text-white font-medium text-sm group-hover:text-yellow-300 transition-colors">
                              Image Prompt Generator
                            </div>
                            <div className="text-gray-400 text-xs">
                              Enhance your AI art prompts
                            </div>
                          </div>
                        </div>
                      </Link>


                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Other Navigation Items */}
            {navItems.filter(item => item.path !== '/').map((item) => {
              const Icon = item.icon;
              return (
                <Link
                  key={item.path}
                  to={item.path}
                  onClick={closeDropdowns}
                  className={`
                    flex items-center gap-2 px-4 py-2 rounded-lg text-sm font-medium
                    transition-all duration-200 hover:scale-105
                    ${isActive(item.path)
                      ? 'bg-purple-900/30 text-purple-300 animate-scale-in'
                      : 'text-gray-300 hover:text-white hover:bg-gray-800'
                    }
                  `}
                >
                  <Icon className="w-4 h-4" />
                  {item.name}
                </Link>
              );
            })}
          </nav>

          {/* Mobile Hamburger Menu Button */}
          <div className="md:hidden">
            <button
              onClick={toggleMobileMenu}
              className="flex items-center justify-center w-10 h-10 rounded-lg text-gray-300 hover:text-white hover:bg-gray-800 transition-colors duration-200"
              aria-label="Toggle mobile menu"
            >
              {isMobileMenuOpen ? (
                <X className="w-6 h-6" />
              ) : (
                <Menu className="w-6 h-6" />
              )}
            </button>
          </div>


        </div>
      </div>

      {/* Mobile Navigation Menu */}
      {isMobileMenuOpen && (
        <div className="md:hidden border-t border-gray-700 bg-gray-800 shadow-lg">
          <div className="px-4 pt-2 pb-3 space-y-1">
            {/* Home Link - First in Mobile */}
            <Link
              to="/"
              onClick={closeMobileMenu}
              className={`
                flex items-center space-x-3 px-3 py-3 rounded-lg text-base font-medium
                transition-all duration-200
                ${isActive('/')
                  ? 'bg-purple-900/30 text-purple-300 border-l-4 border-purple-400'
                  : 'text-gray-300 hover:text-white hover:bg-gray-700'
                }
              `}
            >
              <Home className="w-5 h-5" />
              <span>Home</span>
            </Link>

            {/* Tools Section in Mobile - Second */}
            <div className="mb-4">
              <div className="flex items-center gap-2 px-3 py-2 text-gray-400 text-sm font-semibold uppercase tracking-wider">
                <Settings className="w-4 h-4" />
                Tools
              </div>
              {toolsItems.map((item) => {
                const Icon = item.icon;
                return (
                  <Link
                    key={item.path}
                    to={item.path}
                    onClick={closeMobileMenu}
                    className={`
                      flex items-center space-x-3 px-6 py-3 rounded-lg text-base font-medium
                      transition-all duration-200
                      ${isActive(item.path)
                        ? 'bg-purple-900/30 text-purple-300 border-l-4 border-purple-400'
                        : 'text-gray-300 hover:text-white hover:bg-gray-700'
                      }
                    `}
                  >
                    <Icon className="w-5 h-5" />
                    <span>{item.name}</span>
                  </Link>
                );
              })}
            </div>

            {/* Other Navigation Items in Mobile */}
            {navItems.filter(item => item.path !== '/').map((item) => {
              const Icon = item.icon;
              return (
                <Link
                  key={item.path}
                  to={item.path}
                  onClick={closeMobileMenu}
                  className={`
                    flex items-center space-x-3 px-3 py-3 rounded-lg text-base font-medium
                    transition-all duration-200
                    ${isActive(item.path)
                      ? 'bg-purple-900/30 text-purple-300 border-l-4 border-purple-400'
                      : 'text-gray-300 hover:text-white hover:bg-gray-700'
                    }
                  `}
                >
                  <Icon className="w-5 h-5" />
                  <span>{item.name}</span>
                </Link>
              );
            })}
          </div>
        </div>
      )}
    </header>

    {/* Mobile Menu Overlay */}
    {isMobileMenuOpen && (
      <div
        className="fixed inset-0 bg-black bg-opacity-25 z-30 md:hidden"
        onClick={closeMobileMenu}
      />
    )}
    </>
  );
};

export default Navigation;
