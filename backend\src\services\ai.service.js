const Replicate = require("replicate");
const ImageKit = require("imagekit");
// Using built-in fetch (Node.js 18+)

// Initialize ImageKit (will be initialized when needed to avoid env issues)
let imagekit = null;

// NSFW content filter
const filterNSFWContent = (prompt) => {
    const nsfwWords = [
        'nsfw', 'nude', 'naked', 'sex', 'sexual', 'porn', 'erotic', 'hentai',
        'genitalia', 'breasts', 'nipples', 'inappropriate', 'obscene', 'offensive',
        'suggestive', 'lingerie', 'bikini', 'swimsuit', 'lewd', 'fetish',
        'exposed skin', 'underwear', 'adult content', 'obscene gesture',
        'body fluids', 'explicit','breast'
    ];

    let filteredPrompt = prompt;

    // Sort words by length (longest first) to handle multi-word phrases correctly
    const sortedWords = nsfwWords.sort((a, b) => b.length - a.length);

    // Remove each NSFW word/phrase completely (case-insensitive)
    sortedWords.forEach(word => {
        // Escape special regex characters and handle multi-word phrases
        const escapedWord = word.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
        const regex = new RegExp(`\\b${escapedWord}\\b`, 'gi');
        filteredPrompt = filteredPrompt.replace(regex, '');
    });

    // Clean up extra spaces that might be left after removing words
    filteredPrompt = filteredPrompt.replace(/\s+/g, ' ').trim();

    return filteredPrompt;
};

const initializeImageKit = () => {
    if (!imagekit) {
        imagekit = new ImageKit({
            publicKey: process.env.IMAGEKIT_PUBLIC_KEY,
            privateKey: process.env.IMAGEKIT_PRIVATE_KEY,
            urlEndpoint: process.env.IMAGEKIT_URL_ENDPOINT
        });
    }
    return imagekit;
};

const aiservice = async(prompt, aspectRatio = '1:1')=>{
try {
    // Filter NSFW content from the prompt
    const filteredPrompt = filterNSFWContent(prompt);

    // Step 1: Generate image using Replicate
    const replicate = new Replicate({
        auth: process.env.AI_KEY,
    });

 
   const input = {
                aspect_ratio: aspectRatio,
                prompt: filteredPrompt,
                go_fast: true,
                megapixels: "1",
                num_outputs: 1,
                output_format: "jpg",
                output_quality: 80,
                num_inference_steps: 4
            }

            const output = await replicate.run("black-forest-labs/flux-schnell", { input });
    

            

    // Get the temporary Replicate image URL (convert URL object to string)
    const replicateImageUrl = output[0].url().toString();
    

    // Step 2: Upload image to ImageKit for permanent storage
    try {
        // Initialize ImageKit when needed
        const imagekitInstance = initializeImageKit();

        // Generate a unique filename
        const timestamp = Date.now();
        const fileName = `ai-generated-${timestamp}-${aspectRatio.replace(':', 'x')}.jpg`;

        // Fetch the image from Replicate URL and convert to buffer
        const imageResponse = await fetch(replicateImageUrl);

        if (!imageResponse.ok) {
            throw new Error(`Failed to fetch image: ${imageResponse.status} ${imageResponse.statusText}`);
        }

        // Convert ArrayBuffer to Buffer for ImageKit
        const arrayBuffer = await imageResponse.arrayBuffer();
        const imageBuffer = Buffer.from(arrayBuffer);

        // Upload buffer to ImageKit
        const uploadResponse = await imagekitInstance.upload({
            file: imageBuffer, // Use buffer instead of URL
            fileName: fileName,
            folder: '/ai-generated-images', // Organize images in a folder
            tags: ['ai-generated', `aspect-${aspectRatio}`, 'replicate']
        });

        // Return the permanent ImageKit URL after 2 seconds delay
        return new Promise((resolve) => {
            setTimeout(() => {
                resolve(uploadResponse.url);
            }, 2000); // 5 seconds delay
        });

    } catch (uploadError) {
        // Fallback to original Replicate URL if ImageKit upload fails
        return replicateImageUrl;
    }

} catch (error) {
    throw error; // Re-throw error so controller can handle it properly
}

}
module.exports = aiservice;