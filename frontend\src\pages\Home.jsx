import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import SEO, { pageSEO } from '../components/SEO';
import { Palette, Wand2, Image as ImageIcon, Scissors, FileText, Search, ArrowRight } from 'lucide-react';

import ParticleBackground from '../components/ParticleBackground';

// Shine animation styles and Sparkle button styles
const combinedStyles = `
  @import url('https://fonts.googleapis.com/css2?family=Bungee&family=Oxygen:wght@300;400;700&family=Roboto+Slab:wght@300;400;500;700&family=Nunito:ital,wght@0,200..1000;1,200..1000&display=swap');

  .btn-shine {
    position: relative;
    color: #fff;
    background: linear-gradient(to right, #9f9f9f 0, #fff 10%, #868686 20%);
    background-position: 0;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    animation: shine 4s infinite linear;
    animation-fill-mode: forwards;
    -webkit-text-size-adjust: none;
    text-decoration: none;
    white-space: wrap;
  }

  .oxygen-font {
    font-family: 'Oxygen', sans-serif;
  }

  .roboto-slab-font {
    font-family: 'Roboto Slab', serif;
  }

  /* Sparkle Button Styles */
  .button {
    --black-700: hsla(0 0% 12% / 1);
    --border_radius: 9999px;
    --transtion: 0.3s ease-in-out;
    --offset: 2px;

    cursor: pointer;
    position: relative;

    display: flex;
    align-items: center;
    gap: 0.5rem;

    transform-origin: center;

    padding: 0.5rem 0.5rem;
    background-color: transparent;

    border: none;
    border-radius: var(--border_radius);
    transform: scale(calc(1 + (var(--active, 0) * 0.1)));

    transition: transform var(--transtion);
  }

  .button::before {
    content: "";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);

    width: 100%;
    height: 100%;
    background-color: var(--black-700);

    border-radius: var(--border_radius);
    box-shadow: inset 0 0.5px hsl(0, 0%, 100%), inset 0 -1px 2px 0 hsl(0, 0%, 0%),
      0px 4px 10px -4px hsla(0 0% 0% / calc(1 - var(--active, 0))),
      0 0 0 calc(var(--active, 0) * 0.375rem) hsl(260 97% 50% / 0.75);

    transition: all var(--transtion);
    z-index: 0;
  }

  .button::after {
    content: "";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);

    width: 100%;
    height: 100%;
    background-color: hsla(260 97% 61% / 0.75);
    background-image: radial-gradient(
        at 51% 89%,
        hsla(266, 45%, 74%, 1) 0px,
        transparent 50%
      ),
      radial-gradient(at 100% 100%, hsla(266, 36%, 60%, 1) 0px, transparent 50%),
      radial-gradient(at 22% 91%, hsla(266, 36%, 60%, 1) 0px, transparent 50%);
    background-position: top;

    opacity: var(--active, 0);
    border-radius: var(--border_radius);
    transition: opacity var(--transtion);
    z-index: 2;
  }

  .button:is(:hover, :focus-visible) {
    --active: 1;
  }
  .button:active {
    transform: scale(1);
  }

  .button .dots_border {
    --size_border: calc(100% + 2px);

    overflow: hidden;

    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);

    width: var(--size_border);
    height: var(--size_border);
    background-color: transparent;

    border-radius: var(--border_radius);
    z-index: -10;
  }

  .button .dots_border::before {
    content: "";
    position: absolute;
    top: 30%;
    left: 50%;
    transform: translate(-50%, -50%);
    transform-origin: left;
    transform: rotate(0deg);

    width: 100%;
    height: 2rem;
    background-color: white;

    mask: linear-gradient(transparent 0%, white 120%);
    animation: rotate 2s linear infinite;
  }

  @keyframes rotate {
    to {
      transform: rotate(360deg);
    }
  }

  .button .sparkle {
    position: relative;
    z-index: 10;

    width: 1.75rem;
  }

  .button .sparkle .path {
    fill: currentColor;
    stroke: currentColor;

    transform-origin: center;

    color: hsl(0, 0%, 100%);
  }

  .button:is(:hover, :focus) .sparkle .path {
    animation: path 1.5s linear 0.5s infinite;
  }

  .button .sparkle .path:nth-child(1) {
    --scale_path_1: 1.2;
  }
  .button .sparkle .path:nth-child(2) {
    --scale_path_2: 1.2;
  }
  .button .sparkle .path:nth-child(3) {
    --scale_path_3: 1.2;
  }

  @keyframes path {
    0%,
    34%,
    71%,
    100% {
      transform: scale(1);
    }
    17% {
      transform: scale(var(--scale_path_1, 1));
    }
    49% {
      transform: scale(var(--scale_path_2, 1));
    }
    83% {
      transform: scale(var(--scale_path_3, 1));
    }
  }

  .button .text_button {
    position: relative;
    z-index: 10;

    background-image: linear-gradient(
      90deg,
      hsla(0 0% 100% / 1) 0%,
      hsla(0 0% 100% / var(--active, 0)) 120%
    );
    background-clip: text;

    font-size: 1.5rem;
    font-weight: bold;
    color: transparent;
  }

  /* Shine animation keyframes */
  @-moz-keyframes shine {
    0% {
      background-position: 0;
    }
    60% {
      background-position: 680px;
    }
    100% {
      background-position: 1200px;
    }
  }
  @-webkit-keyframes shine {
    0% {
      background-position: 0;
    }
    60% {
      background-position: 680px;
    }
    100% {
      background-position: 1200px;
    }
  }
  @-o-keyframes shine {
    0% {
      background-position: 0;
    }
    60% {
      background-position: 580px;
    }
    100% {
      background-position: 1100px;
    }
  }
  @keyframes shine {
    0% {
      background-position: 0;
    }
    60% {
      background-position: 580px;
    }
    100% {
      background-position: 1100px;
    }
  }

  /* Website Theme Match Styles */
  .hero-card {
    display: flex;
    flex-direction: column;
    position: relative;
    width: 320px;
    min-width: 320px;
    height: 350px;
    border-radius: 16px;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
    margin: 8px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(16px);
    cursor: pointer;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.2);
    padding: 24px;
  }

  .hero-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 32px rgba(0, 0, 0, 0.18);
    background: rgba(255, 255, 255, 0.15);
    border: 2px solid rgba(59, 130, 246, 0.6);
  }

  .hero-icon {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20px;
    color: white;
    font-weight: 600;
    background: #7c3aed;
  }

  .hero-title {
    font-size: 30px;
    font-weight: 700;
    color: #ffffff;
    margin-bottom: 12px;
    line-height: 1.3;
    text-align: left;
  }

  .hero-description {
    font-size: 21px;
    line-height: 1.5;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: auto;
    flex-grow: 1;
    text-align: left;
  }

  .hero-stats {
    display: flex;
    gap: 16px;
    align-items: center;
    margin-top: 20px;
  }

  .hero-stat {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 14px;
    font-weight: 500;
    color: rgba(255, 255, 255, 0.7);
  }

  .hero-stat svg {
    width: 16px;
    height: 16px;
  }

  /* Curved Path Styles */
  .curved-path {
    position: relative;
  }

  .curved-path::before {
    content: '';
    position: absolute;
    top: 0;
    left: 50%;
    width: 4px;
    height: 100%;
    background: linear-gradient(180deg,
      #8b5cf6 0%,
      #06b6d4 25%,
      #10b981 50%,
      #f59e0b 75%,
      #6366f1 100%);
    border-radius: 2px;
    transform: translateX(-50%);
    z-index: 1;
  }

  .tool-card {
    position: relative;
    z-index: 10;
  }

  .tool-card:nth-child(odd) {
    margin-left: 0;
    margin-right: auto;
  }

  .tool-card:nth-child(even) {
    margin-left: auto;
    margin-right: 0;
  }

  @media (max-width: 1023px) {
    .curved-path::before {
      display: none;
    }

    .tool-card:nth-child(odd),
    .tool-card:nth-child(even) {
      margin-left: auto;
      margin-right: auto;
    }
  }
`;

// Welcome Screen Component
const WelcomeScreen = () => {
  const navigate = useNavigate();













  return (
    <div className="min-h-screen relative">
      {/* Particle Background */}
      <ParticleBackground />

      {/* Add shine animation and sparkle button styles */}
      <style dangerouslySetInnerHTML={{ __html: combinedStyles }} />

      {/* Hero Section */}
      <div className="max-w-6xl mx-auto px-4 pt-8 pb-16 relative z-10">
        <div className="text-center">
          {/* Main Title */}
          <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold mb-6 animate-fade-in-up">
            <span className="btn-shine">Your Free AI Creative Studio – All </span>
             
            
            <span className="bg-gradient-to-r from-blue-400 via-purple-500 to-pink-500 bg-clip-text text-transparent">Tools in One Place</span>
          </h1>

          {/* Subtitle */}
          <h2 className="text-2xl md:text-3xl lg:text-4xl font-semibold text-white mt-10 mb-8 animate-fade-in-up oxygen-font" style={{ animationDelay: '0s' }}>
           Background Removal, Upscaling images, Image to Prompt & More in One Place

          </h2>



          {/* AI Tools Cards */}
          <div className="mb-16 animate-fade-in-up" style={{ animationDelay: '0s' }}>
            <div className="max-w-7xl mx-auto px-4">
              <div className="flex flex-wrap justify-center gap-4">

                {/* Background Removal Card */}
                <div className="hero-card">
                  <div className="hero-icon">
                    <Scissors className="w-6 h-6" />
                  </div>
                  <h3 className="hero-title">Background Removal</h3>
                  <p className="hero-description">Remove backgrounds from images instantly with AI. </p>
                  <button
                    onClick={() => navigate('/background-remove')}
                    className="mb-3 px-4 py-3 bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white text-sm font-medium rounded-lg transition-all duration-200 flex items-center justify-center gap-2 cursor-pointer"
                  >
                    Try Now
                    <ArrowRight className="w-4 h-4" />
                  </button>

                </div>

                {/* Image to Prompt Card */}
                <div className="hero-card">
                  <div className="hero-icon">
                    <ImageIcon className="w-6 h-6" />
                  </div>
                  <h3 className="hero-title">Image to Prompt</h3>
                  <p className="hero-description">Generate detailed prompts from images.</p>
                  <button
                    onClick={() => navigate('/image-to-prompt')}
                    className="mb-3 px-4 py-3 bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white text-sm font-medium rounded-lg transition-all duration-200 flex items-center justify-center gap-2 cursor-pointer"
                  >
                    Try Now
                    <ArrowRight className="w-4 h-4" />
                  </button>

                </div>

                {/* Image Upscaling Card */}
                <div className="hero-card">
                  <div className="hero-icon">
                    <Search className="w-6 h-6" />
                  </div>
                  <h3 className="hero-title">Image Upscaling</h3>
                  <p className="hero-description">Enhance image resolution up to 4k using AI.</p>
                  <button
                    onClick={() => navigate('/upscale-image')}
                    className="mb-3 px-4 py-3 bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white text-sm font-medium rounded-lg transition-all duration-200 flex items-center justify-center gap-2 cursor-pointer"
                  >
                    Try Now
                    <ArrowRight className="w-4 h-4" />
                  </button>

                </div>



                {/* Logo Prompt Card */}
                <div className="hero-card">
                  <div className="hero-icon">
                    <Palette className="w-6 h-6" />
                  </div>
                  <h3 className="hero-title">Logo Prompt Generator</h3>
                  <p className="hero-description">Generate professional logo design prompts for brand.</p>
                  <button
                    onClick={() => navigate('/logo-prompt')}
                    className="mb-3 px-4 py-3 bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white text-sm font-medium rounded-lg transition-all duration-200 flex items-center justify-center gap-2 cursor-pointer"
                  >
                    Try Now
                    <ArrowRight className="w-4 h-4" />
                  </button>

                </div>

                {/* Image Prompt Generator Card */}
                <div className="hero-card">
                  <div className="hero-icon">
                    <FileText className="w-6 h-6" />
                  </div>
                  <h3 className="hero-title">Image Prompt Generator</h3>
                  <p className="hero-description">Transform basic ideas into detailed prompts</p>
                  <button
                    onClick={() => navigate('/image-prompt-generator')}
                    className="mb-3 px-4 py-3 bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white text-sm font-medium rounded-lg transition-all duration-200 flex items-center justify-center gap-2 cursor-pointer"
                  >
                    Try Now
                    <ArrowRight className="w-4 h-4" />
                  </button>

                </div>

              </div>
            </div>
          </div>

        </div>
      </div>

      {/* AI Tools Section */}
      <section className="w-full py-20 relative z-10" id="tools" style={{ backgroundColor: '#101828' }}>
        <div className="max-w-7xl mx-auto px-6">
          <div className="text-center mb-20">
            <h2 className="text-4xl lg:text-5xl font-bold text-white mb-6">
             Your AI Powered Tools
            </h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
          A complete suite of AI tools covering every aspect of your creation journey
            </p>
          </div>

          <div className="curved-path relative">
            {/* Background Remover */}
            <div className="tool-card relative z-10 flex flex-col lg:flex-row-reverse items-center gap-12 mb-32 bg-white/10 backdrop-blur-md rounded-3xl p-8 lg:p-12 border border-white/20 shadow-lg max-w-5xl ml-auto"
                 style={{ opacity: 1, transform: 'translateY(0px)', transition: 'opacity 0.6s, transform 0.6s' }}>
              <div className="flex-1 lg:pl-8">
                <div className="w-20 h-20 bg-gradient-to-r from-cyan-500 to-blue-500 rounded-full flex items-center justify-center mb-6">
                  <Scissors className="text-3xl text-white w-8 h-8" />
                </div>
                <h3 className="text-3xl lg:text-4xl font-bold text-white mb-4">Background Remover</h3>
                <p className="text-lg text-gray-300 mb-6 leading-relaxed">
                  Instantly remove image backgrounds with precision. Get clean, transparent PNGs perfect for presentations, e-commerce, and creative projects.
                </p>
                <ul className="space-y-3 mb-8">
                  <li className="flex items-center text-gray-300">
                    <div className="w-2 h-2 bg-cyan-500 rounded-full mr-4"></div>
                    One-click automatic background removal
                  </li>
                  <li className="flex items-center text-gray-300">
                    <div className="w-2 h-2 bg-cyan-500 rounded-full mr-4"></div>
                    Advanced edge detection technology
                  </li>
                  <li className="flex items-center text-gray-300">
                    <div className="w-2 h-2 bg-cyan-500 rounded-full mr-4"></div>
                    Change background color one click
                  </li>
                </ul>
                <button
                  onClick={() => navigate('/background-remove')}
                  className="bg-gradient-to-r from-cyan-500 to-blue-500 text-white px-8 py-3 rounded-full font-semibold hover:from-cyan-600 hover:to-blue-600 transition-all duration-300 transform hover:scale-105 shadow-lg"
                >
                  Try Background Remover
                </button>
              </div>
              <div className="flex-1">
                <img
                  src="https://ik.imagekit.io/q0mafimea/Before.png?updatedAt=1760793022106"
                  alt="Background Remover"
                  className="w-full h-80 object-cover rounded-2xl shadow-lg"
                />
              </div>
            </div>

            {/* Image to Prompt Generator */}
            <div className="tool-card relative z-10 flex flex-col lg:flex-row items-center gap-12 mb-32 bg-white/10 backdrop-blur-md rounded-3xl p-8 lg:p-12 border border-white/20 shadow-lg max-w-5xl"
                 style={{ opacity: 1, transform: 'translateY(0px)', transition: 'opacity 0.6s 0.1s, transform 0.6s 0.1s' }}>
              <div className="flex-1 lg:pr-8">
                <div className="w-20 h-20 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-full flex items-center justify-center mb-6">
                  <ImageIcon className="text-3xl text-white w-8 h-8" />
                </div>
                <h3 className="text-3xl lg:text-4xl font-bold text-white mb-4">Image to Prompt Generator</h3>
                <p className="text-lg text-gray-300 mb-6 leading-relaxed">
                  Reverse-engineer any image into detailed AI prompts. Perfect for understanding artistic styles and recreating similar visuals with precision.
                </p>
                <ul className="space-y-3 mb-8">
                  <li className="flex items-center text-gray-300">
                    <div className="w-2 h-2 bg-indigo-500 rounded-full mr-4"></div>
                    Detailed image analysis and description
                  </li>
                  <li className="flex items-center text-gray-300">
                    <div className="w-2 h-2 bg-indigo-500 rounded-full mr-4"></div>
                    Copy-ready prompts for AI generation
                  </li>
                  <li className="flex items-center text-gray-300">
                    <div className="w-2 h-2 bg-indigo-500 rounded-full mr-4"></div>
                    Artistic style identification and tagging
                  </li>
                </ul>
                <button
                  onClick={() => navigate('/image-to-prompt')}
                  className="bg-gradient-to-r from-indigo-500 to-purple-500 text-white px-8 py-3 rounded-full font-semibold hover:from-indigo-600 hover:to-purple-600 transition-all duration-300 transform hover:scale-105 shadow-lg"
                >
                  Try Image to Prompt
                </button>
              </div>
              <div className="flex-1">
                <img
                  src="https://ik.imagekit.io/q0mafimea/4.png?updatedAt=1760793053852"
                  alt="Image to Prompt Generator"
                  className="w-full h-80 object-cover rounded-2xl shadow-lg"
                />
              </div>
            </div>

            {/* AI Image Upscaler */}
            <div className="tool-card relative z-10 flex flex-col lg:flex-row items-center gap-12 mb-32 bg-white/10 backdrop-blur-md rounded-3xl p-8 lg:p-12 border border-white/20 shadow-lg max-w-5xl"
                 style={{ opacity: 1, transform: 'translateY(0px)', transition: 'opacity 0.6s 0.2s, transform 0.6s 0.2s' }}>
              <div className="flex-1 lg:pr-8">
                <div className="w-20 h-20 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full flex items-center justify-center mb-6">
                  <Search className="text-3xl text-white w-8 h-8" />
                </div>
                <h3 className="text-3xl lg:text-4xl font-bold text-white mb-4">AI Image Upscaler</h3>
                <p className="text-lg text-gray-300 mb-6 leading-relaxed">
                  Enhance image resolution up to 4x without losing detail. Transform low-quality images into crisp, high-definition masterpieces.
                </p>
                <ul className="space-y-3 mb-8">
                  <li className="flex items-center text-gray-300">
                    <div className="w-2 h-2 bg-green-500 rounded-full mr-4"></div>
                    4x resolution boost with AI enhancement
                  </li>
                  <li className="flex items-center text-gray-300">
                    <div className="w-2 h-2 bg-green-500 rounded-full mr-4"></div>
                    Detail preservation and sharpening
                  </li>
                  <li className="flex items-center text-gray-300">
                    <div className="w-2 h-2 bg-green-500 rounded-full mr-4"></div>
                    Intelligent noise reduction technology
                  </li>
                </ul>
                <button
                  onClick={() => navigate('/upscale-image')}
                  className="bg-gradient-to-r from-green-500 to-emerald-500 text-white px-8 py-3 rounded-full font-semibold hover:from-green-600 hover:to-emerald-600 transition-all duration-300 transform hover:scale-105 shadow-lg"
                >
                  Try Image Upscaler
                </button>
              </div>
              <div className="flex-1">
                <img
                  src="https://ik.imagekit.io/q0mafimea/1.png?updatedAt=1760793055153"
                  alt="AI Image Upscaler"
                  className="w-full h-80 object-cover rounded-2xl shadow-lg"
                />
              </div>
            </div>




            {/* Logo Prompt Generator */}
            <div className="tool-card relative z-10 flex flex-col lg:flex-row-reverse items-center gap-12 mb-32 bg-white/10 backdrop-blur-md rounded-3xl p-8 lg:p-12 border border-white/20 shadow-lg max-w-5xl"
                 style={{ opacity: 1, transform: 'translateY(0px)', transition: 'opacity 0.6s 0.5s, transform 0.6s 0.5s' }}>
              <div className="flex-1 lg:pl-8">
                <div className="w-20 h-20 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center mb-6">
                  <Palette className="text-3xl text-white w-8 h-8" />
                </div>
                <h3 className="text-3xl lg:text-4xl font-bold text-white mb-4">Free AI logo design prompt generator</h3>
                <p className="text-lg text-gray-300 mb-6 leading-relaxed">
                  Transform your brand vision into creative logo design prompts. Get 5 unique AI-optimized prompts for professional logo creation.
                </p>
                <ul className="space-y-3 mb-8">
                  <li className="flex items-center text-gray-300">
                    <div className="w-2 h-2 bg-purple-500 rounded-full mr-4"></div>
                    Brand-specific logo design prompts
                  </li>
                  <li className="flex items-center text-gray-300">
                    <div className="w-2 h-2 bg-purple-500 rounded-full mr-4"></div>
                    5 unique styles and approaches
                  </li>
                  <li className="flex items-center text-gray-300">
                    <div className="w-2 h-2 bg-purple-500 rounded-full mr-4"></div>
                    Ready for Midjourney, DALL-E, and more
                  </li>
                </ul>
                <button
                  onClick={() => navigate('/logo-prompt')}
                  className="bg-gradient-to-r from-purple-500 to-pink-500 text-white px-8 py-3 rounded-full font-semibold hover:from-purple-600 hover:to-pink-600 transition-all duration-300 transform hover:scale-105 shadow-lg"
                >
                  Try Logo Prompt
                </button>
              </div>
              <div className="flex-1">
                <img
                  src="https://ik.imagekit.io/q0mafimea/Brand%20name%20(2).png?updatedAt=1763189799294"
                  alt="Free AI logo design prompt generator"
                  className="w-full h-80 object-cover rounded-2xl shadow-lg"
                />
              </div>
            </div>

            {/* Image Prompt Generator */}
            <div className="tool-card relative z-10 flex flex-col lg:flex-row items-center gap-12 bg-white/10 backdrop-blur-md rounded-3xl p-8 lg:p-12 border border-white/20 shadow-lg max-w-5xl"
                 style={{ opacity: 1, transform: 'translateY(0px)', transition: 'opacity 0.6s 0.6s, transform 0.6s 0.6s' }}>
              <div className="flex-1 lg:pr-8">
                <div className="w-20 h-20 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-full flex items-center justify-center mb-6">
                  <Wand2 className="text-3xl text-white w-8 h-8" />
                </div>
                <h3 className="text-3xl lg:text-4xl font-bold text-white mb-4">Image Prompt Generator</h3>
                <p className="text-lg text-gray-300 mb-6 leading-relaxed">
                  Transform basic ideas into detailed, professional image generation prompts. Perfect for getting better results from AI art tools.
                </p>
                <ul className="space-y-3 mb-8">
                  <li className="flex items-center text-gray-300">
                    <div className="w-2 h-2 bg-yellow-500 rounded-full mr-4"></div>
                    Professional prompt enhancement
                  </li>
                  <li className="flex items-center text-gray-300">
                    <div className="w-2 h-2 bg-yellow-500 rounded-full mr-4"></div>
                    20+ artistic styles to choose from
                  </li>
                  <li className="flex items-center text-gray-300">
                    <div className="w-2 h-2 bg-yellow-500 rounded-full mr-4"></div>
                    Optimized for DALL-E, Midjourney, Stable Diffusion
                  </li>
                </ul>
                <button
                  onClick={() => navigate('/image-prompt-generator')}
                  className="bg-gradient-to-r from-yellow-500 to-orange-500 text-white px-8 py-3 rounded-full font-semibold hover:from-yellow-600 hover:to-orange-600 transition-all duration-300 transform hover:scale-105 shadow-lg"
                >
                  Try Image Prompt Generator
                </button>
              </div>
              <div className="flex-1">
                <img
                  src="https://ik.imagekit.io/q0mafimea/Add%20a%20heading%20(19).png?updatedAt=1763189799686"
                  alt="Image Prompt Generator"
                  className="w-full h-80 object-cover rounded-2xl shadow-lg"
                />
              </div>
            </div>

          </div>
        </div>
      </section>





     
     



    </div>
  );
};

const Home = () => {
  useEffect(() => {
    // Scroll to top when page loads
    window.scrollTo({ top: 0, behavior: 'smooth' });
  }, []);

  return (
    <div className="relative min-h-screen">
      {/* SEO */}
      <SEO
        title={pageSEO.home.title}
        description={pageSEO.home.description}
        keywords={pageSEO.home.keywords}
      />

      {/* Background gradient - Dark theme like PhotoGPT */}
      <div className="absolute inset-0 bg-gradient-to-br from-gray-900 via-gray-800 to-black -z-10"></div>

      {/* Subtle pattern overlay */}
      <div className="absolute inset-0 opacity-10 -z-5" style={{
        backgroundImage: `radial-gradient(circle at 25% 25%, rgba(59, 130, 246, 0.3) 0%, transparent 50%),
                         radial-gradient(circle at 75% 75%, rgba(147, 51, 234, 0.3) 0%, transparent 50%)`
      }}></div>

      {/* Content container */}
      <div className="relative z-10">
        <WelcomeScreen />
      </div>
    </div>
  );
};

export default Home;
