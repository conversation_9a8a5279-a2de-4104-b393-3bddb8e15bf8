const Tool = require('../../models/Tool');

// Get all tools with their stats
const getAllTools = async (req, res) => {
  try {
    const tools = await Tool.find({ isActive: true })
      .select('name slug description icon views loves route')
      .sort({ views: -1 });
    
    res.status(200).json({
      success: true,
      data: tools
    });
  } catch (error) {
    console.error('Error fetching tools:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch tools',
      error: error.message
    });
  }
};

// Get a specific tool by slug
const getToolBySlug = async (req, res) => {
  try {
    const { slug } = req.params;
    const tool = await Tool.findOne({ slug, isActive: true });
    
    if (!tool) {
      return res.status(404).json({
        success: false,
        message: 'Tool not found'
      });
    }
    
    res.status(200).json({
      success: true,
      data: tool
    });
  } catch (error) {
    console.error('Error fetching tool:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch tool',
      error: error.message
    });
  }
};

// Increment view count for a tool
const incrementViews = async (req, res) => {
  try {
    const { slug } = req.params;
    
    const tool = await Tool.findOneAndUpdate(
      { slug, isActive: true },
      { $inc: { views: 1 } },
      { new: true, runValidators: true }
    );
    
    if (!tool) {
      return res.status(404).json({
        success: false,
        message: 'Tool not found'
      });
    }
    
    res.status(200).json({
      success: true,
      message: 'View count updated',
      data: {
        slug: tool.slug,
        views: tool.views
      }
    });
  } catch (error) {
    console.error('Error updating view count:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update view count',
      error: error.message
    });
  }
};

// Increment or decrement love count for a tool
const incrementLoves = async (req, res) => {
  try {
    const { slug } = req.params;
    const { increment = 1 } = req.body; // Default to 1 if not provided

    const tool = await Tool.findOneAndUpdate(
      { slug, isActive: true },
      { $inc: { loves: increment } },
      { new: true, runValidators: true }
    );

    if (!tool) {
      return res.status(404).json({
        success: false,
        message: 'Tool not found'
      });
    }

    // Ensure loves doesn't go below 0
    if (tool.loves < 0) {
      tool.loves = 0;
      await tool.save();
    }

    res.status(200).json({
      success: true,
      message: 'Love count updated',
      data: {
        slug: tool.slug,
        loves: tool.loves
      }
    });
  } catch (error) {
    console.error('Error updating love count:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update love count',
      error: error.message
    });
  }
};

module.exports = {
  getAllTools,
  getToolBySlug,
  incrementViews,
  incrementLoves
};
