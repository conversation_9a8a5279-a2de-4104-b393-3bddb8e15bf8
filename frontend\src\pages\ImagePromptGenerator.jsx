import React, { useState } from 'react';
import axios from 'axios';
import SEO, { pageSEO } from '../components/SEO';
import { Wand2, <PERSON>rk<PERSON>, <PERSON><PERSON>, CheckCircle, AlertCircle, Loader2, Cloud, <PERSON><PERSON>, Type, ChevronDown, MessageCircle } from 'lucide-react';

// FeatureCard Component
function FeatureCard({ icon, title, description }) {
  return (
    <div className="bg-gray-800/30 backdrop-blur-md rounded-2xl p-6 border border-gray-700/30 hover:border-purple-500/30 transition-all duration-300 group">
      <div className="text-4xl mb-4 group-hover:scale-110 transition-transform duration-300">
        {icon}
      </div>
      <h3 className="text-xl font-semibold text-white mb-3 group-hover:text-purple-300 transition-colors duration-300">
        {title}
      </h3>
      <p className="text-gray-400 leading-relaxed">
        {description}
      </p>
    </div>
  );
}

// FAQItem Component
function FAQItem({ question, answer, isOpen, onClick }) {
  return (
    <div className="bg-gray-800/30 backdrop-blur-md rounded-2xl border border-gray-700/30 overflow-hidden hover:border-purple-500/30 transition-all duration-300">
      <button
        onClick={onClick}
        className="w-full px-6 py-5 text-left flex items-center justify-between hover:bg-gray-700/20 transition-colors duration-200"
      >
        <h3 className="text-lg font-semibold text-white pr-4">{question}</h3>
        <ChevronDown
          className={`w-5 h-5 text-purple-400 transition-transform duration-300 flex-shrink-0 ${
            isOpen ? 'rotate-180' : ''
          }`}
        />
      </button>
      {isOpen && (
        <div className="px-6 pb-5">
          <div className="pt-2 border-t border-gray-700/30">
            <p className="text-gray-300 leading-relaxed">{answer}</p>
          </div>
        </div>
      )}
    </div>
  );
}

const ImagePromptGenerator = () => {
  const [rawPrompt, setRawPrompt] = useState('');
  const [selectedStyle, setSelectedStyle] = useState('');
  const [enhancedPrompt, setEnhancedPrompt] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(false);
  const [copied, setCopied] = useState(false);
  const [isStyleDropdownOpen, setIsStyleDropdownOpen] = useState(false);

  // FAQ State
  const [openFAQ, setOpenFAQ] = useState(0);

  const imageStyles = [
    'Photorealistic',
    'Digital Art',
    'Oil Painting',
    'Watercolor',
    'Anime/Manga',
    'Cyberpunk',
    'Steampunk',
    'Fantasy Art',
    'Impressionist',
    'Surrealism',
    'Low Poly',
    'Pixel Art',
    'Charcoal Sketch',
    'Cybernetic',
    'Retro Futurism',
    'Art Nouveau',
    'Ukiyo-e',
    'Isometric',
    'Claymation',
    'Vaporwave',
    'Black and White'
  ];

  const generateEnhancedPrompt = async () => {
    if (!rawPrompt.trim()) {
      setError('Please enter your raw prompt');
      return;
    }

    if (!selectedStyle) {
      setError('Please select an image style');
      return;
    }

    setIsProcessing(true);
    setError(null);
    setSuccess(false);

    try {
      const response = await axios.post(`${import.meta.env.VITE_API_BASE_URL}/api/ai/image-prompt-generator`, {
        rawPrompt: rawPrompt.trim(),
        selectedStyle: selectedStyle
      });

      if (response.data.success) {
        setEnhancedPrompt(response.data.enhancedPrompt);
        setSuccess(true);
      } else {
        setError(response.data.error || 'Failed to generate enhanced prompt');
      }
    } catch (error) {
      console.error('Error generating enhanced prompt:', error);
      setError(error.response?.data?.error || 'Failed to generate enhanced prompt');
    } finally {
      setIsProcessing(false);
    }
  };

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(enhancedPrompt);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy text: ', err);
    }
  };

  const clearAll = () => {
    setRawPrompt('');
    setSelectedStyle('');
    setEnhancedPrompt('');
    setError(null);
    setSuccess(false);
    setCopied(false);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-purple-900/20 to-gray-900">
      {/* SEO */}
      <SEO
        title={pageSEO.imagePromptGenerator.title}
        description={pageSEO.imagePromptGenerator.description}
        keywords={pageSEO.imagePromptGenerator.keywords}
      />

      {/* Background gradient */}
      <div className="absolute inset-0 bg-gradient-to-br from-purple-900/20 via-blue-900/20 to-indigo-900/20 -z-10"></div>

      {/* Content */}
      <div className="relative z-10 py-8 px-4 sm:px-6 lg:px-8 max-w-6xl mx-auto">
        {/* Header */}
        <div className="text-center mb-8 sm:mb-12">
          <div className="flex items-center justify-center gap-3 mb-4">
            <div className="p-3 bg-gradient-to-br from-purple-600 to-pink-600 rounded-2xl shadow-lg">
              <Wand2 className="w-8 h-8 text-white" />
            </div>
            <h1 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-white">
              Image Prompt Generator
            </h1>
          </div>
          <p className="text-lg sm:text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
            Transform your basic ideas into detailed, professional image generation prompts optimized for AI art tools like DALL-E, Midjourney, and Stable Diffusion.
          </p>
        </div>

        {/* Error Message */}
        {error && (
          <div className="mb-6 p-4 bg-red-900/50 border border-red-700/50 rounded-xl flex items-center gap-3">
            <AlertCircle className="w-5 h-5 text-red-400 flex-shrink-0" />
            <p className="text-red-200 text-sm sm:text-base">{error}</p>
          </div>
        )}

        {/* Success Message */}
        {success && (
          <div className="mb-6 p-4 bg-green-900/50 border border-green-700/50 rounded-xl flex items-center gap-3">
            <CheckCircle className="w-5 h-5 text-green-400 flex-shrink-0" />
            <p className="text-green-200 text-sm sm:text-base">
              Successfully generated enhanced prompt! Copy and use it in your favorite AI image generator.
            </p>
          </div>
        )}

        {/* Main Content - Side by Side Layout */}
        <div className="grid lg:grid-cols-2 gap-6">
          {/* Input Section - Left Side */}
          <div className="bg-gray-800/50 backdrop-blur-md rounded-2xl sm:rounded-3xl p-4 sm:p-6 lg:p-8 border border-gray-700/50 shadow-2xl">
            <div className="flex items-center gap-2 mb-6">
              <Type className="w-5 h-5 text-purple-400" />
              <h2 className="text-lg sm:text-xl font-semibold text-white">Prompt Details</h2>
            </div>

            <div className="space-y-6">
              {/* Raw Prompt Input */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Raw Prompt *
                </label>
                <textarea
                  value={rawPrompt}
                  onChange={(e) => setRawPrompt(e.target.value)}
                  placeholder="Enter your basic idea... e.g., 'a cat sitting on a chair', 'futuristic city at sunset', 'portrait of a warrior'"
                  rows={4}
                  className="w-full px-4 py-3 bg-gray-700/50 border border-gray-600/50 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200 resize-none"
                  maxLength={500}
                />
                <div className="text-right text-xs text-gray-400 mt-1">
                  {rawPrompt.length}/500
                </div>
              </div>

              {/* Style Selection */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Image Style *
                </label>
                <div className="relative">
                  <button
                    type="button"
                    onClick={() => setIsStyleDropdownOpen(!isStyleDropdownOpen)}
                    className="w-full px-4 py-3 bg-gray-700/50 border border-gray-600/50 rounded-xl text-white focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200 flex items-center justify-between"
                  >
                    <span className={selectedStyle ? 'text-white' : 'text-gray-400'}>
                      {selectedStyle || 'Select an image style...'}
                    </span>
                    <ChevronDown className={`w-5 h-5 text-gray-400 transition-transform duration-200 ${isStyleDropdownOpen ? 'rotate-180' : ''}`} />
                  </button>
                  
                  {isStyleDropdownOpen && (
                    <div className="absolute top-full left-0 right-0 mt-2 bg-gray-700 border border-gray-600 rounded-xl shadow-xl z-50 max-h-60 overflow-y-auto">
                      {imageStyles.map((style) => (
                        <button
                          key={style}
                          onClick={() => {
                            setSelectedStyle(style);
                            setIsStyleDropdownOpen(false);
                          }}
                          className="w-full px-4 py-3 text-left text-white hover:bg-gray-600 transition-colors duration-200 first:rounded-t-xl last:rounded-b-xl"
                        >
                          {style}
                        </button>
                      ))}
                    </div>
                  )}
                </div>
              </div>

              {/* Generate Button */}
              <div className="flex flex-col gap-3">
                <button
                  onClick={generateEnhancedPrompt}
                  disabled={isProcessing || !rawPrompt.trim() || !selectedStyle}
                  className="w-full py-3 sm:py-4 bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 disabled:from-gray-600 disabled:to-gray-700 text-white rounded-2xl font-semibold transition-all duration-200 flex items-center justify-center gap-2 cursor-pointer disabled:cursor-not-allowed shadow-lg text-sm sm:text-base"
                >
                  {isProcessing ? (
                    <>
                      <Loader2 className="w-4 h-4 sm:w-5 sm:h-5 animate-spin" />
                      Enhancing Prompt...
                    </>
                  ) : (
                    <>
                      <Sparkles className="w-4 h-4 sm:w-5 sm:h-5" />
                      Generate Enhanced Prompt
                    </>
                  )}
                </button>

                {enhancedPrompt && (
                  <button
                    onClick={clearAll}
                    className="w-full py-3 bg-gray-600/80 hover:bg-gray-700/80 text-white rounded-2xl font-semibold transition-all duration-200 text-sm sm:text-base"
                  >
                    Clear All
                  </button>
                )}
              </div>
            </div>
          </div>

          {/* Output Section - Right Side */}
          <div className="bg-gray-800/50 backdrop-blur-md rounded-2xl sm:rounded-3xl p-4 sm:p-6 lg:p-8 border border-gray-700/50 shadow-2xl">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center gap-2">
                <Sparkles className="w-5 h-5 text-purple-400" />
                <h2 className="text-lg sm:text-xl font-semibold text-white">Enhanced Prompt</h2>
              </div>
              
              {enhancedPrompt && (
                <button
                  onClick={copyToClipboard}
                  className="px-3 py-2 bg-purple-600/80 hover:bg-purple-700/80 text-white rounded-lg transition-all duration-200 flex items-center gap-2 text-sm"
                >
                  <Copy className="w-4 h-4" />
                  {copied ? 'Copied!' : 'Copy'}
                </button>
              )}
            </div>

            <div className="h-[500px] bg-gray-900/50 rounded-xl p-4 border border-gray-600/30 overflow-auto">
              {isProcessing ? (
                <div className="flex flex-col items-center justify-center h-full text-gray-400">
                  <div className="typewriter mb-6">
                    <div className="slide"><i></i></div>
                    <div className="paper"></div>
                    <div className="keyboard"></div>
                  </div>
                  <p className="text-lg font-medium mb-2">Enhancing Your Prompt</p>
                  <p className="text-sm text-center">
                    AI is transforming your idea into a detailed, professional prompt...
                  </p>
                </div>
              ) : enhancedPrompt ? (
                <div className="space-y-4">
                  <div className="text-gray-200 text-sm sm:text-base leading-relaxed whitespace-pre-wrap">
                    {enhancedPrompt}
                  </div>
                </div>
              ) : (
                <div className="flex flex-col items-center justify-center h-full text-gray-500">
                  <Palette className="w-12 h-12 mb-4" />
                  <p className="text-lg font-medium mb-2">Ready to Enhance Your Prompt</p>
                  <p className="text-sm text-center">
                    Enter your raw prompt and select a style to generate a detailed, professional image prompt.
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Pro Tips Section */}
        <div className="mt-12 bg-gray-800/30 backdrop-blur-md rounded-2xl p-6 border border-gray-700/30">
          <h3 className="text-xl font-semibold text-white mb-4 flex items-center gap-2">
            <Wand2 className="w-5 h-5 text-purple-400" />
            Pro Tips for Better Results
          </h3>
          <div className="grid md:grid-cols-2 gap-4 text-gray-300">
            <div className="space-y-2">
              <p className="font-medium text-purple-300">• Be Specific</p>
              <p className="text-sm">Include details about subjects, objects, and settings in your raw prompt.</p>
            </div>
            <div className="space-y-2">
              <p className="font-medium text-purple-300">• Choose the Right Style</p>
              <p className="text-sm">Select a style that matches your vision - it greatly affects the final result.</p>
            </div>
            <div className="space-y-2">
              <p className="font-medium text-purple-300">• Use Action Words</p>
              <p className="text-sm">Describe what's happening in the scene for more dynamic results.</p>
            </div>
            <div className="space-y-2">
              <p className="font-medium text-purple-300">• Consider Composition</p>
              <p className="text-sm">Mention camera angles, lighting, or mood for professional-looking images.</p>
            </div>
          </div>
        </div>

        {/* What is Image Prompt Generator Section */}
        <div className="max-w-7xl mx-auto px-4 py-20 sm:px-6 lg:px-8">
          {/* Header Section */}
          <div className="text-center max-w-4xl mx-auto mb-20">
            <h1 className="text-5xl md:text-6xl lg:text-7xl font-bold text-white mb-8">
              What is <span className="text-purple-400 inline-block relative">
                Image Prompt Generator?
                <span className="absolute bottom-0 left-0 w-full h-2 bg-purple-500/20 rounded-full"></span>
              </span>
            </h1>
            <p className="text-xl md:text-2xl text-gray-400 leading-relaxed">
              Image Prompt Generator transforms your basic ideas into detailed, professional prompts optimized for AI image generators. Turn simple concepts into rich, descriptive prompts that produce stunning visual results.
            </p>
          </div>

          {/* Features Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-32">
            <FeatureCard
              icon="✨"
              title="AI-Enhanced Prompts"
              description="Transform basic ideas into detailed, professional prompts with rich descriptions and technical specifications."
            />
            <FeatureCard
              icon="🎨"
              title="Style-Specific Optimization"
              description="Choose from 20+ art styles to get prompts perfectly tailored for your desired aesthetic and medium."
            />
            <FeatureCard
              icon="🔧"
              title="Technical Precision"
              description="Includes camera settings, lighting details, and composition guidelines for professional-quality results."
            />
            <FeatureCard
              icon="⚡"
              title="Instant Generation"
              description="Get enhanced prompts in seconds. No prompt engineering experience needed - just describe your vision."
            />
            <FeatureCard
              icon="🎯"
              title="AI Tool Compatible"
              description="Optimized for DALL-E, Midjourney, Stable Diffusion, and other popular AI image generators."
            />
            <FeatureCard
              icon="💡"
              title="Creative Expansion"
              description="Discover new creative possibilities by expanding your simple ideas into rich, detailed concepts."
            />
          </div>

          {/* FAQ Section */}
          <div className="max-w-5xl mx-auto mb-20">
            <div className="text-center mb-16">
              <h2 className="text-5xl md:text-6xl font-bold text-white mb-6">Frequently Asked Questions</h2>
              <p className="text-xl text-gray-400">Everything you need to know about Image Prompt Generator</p>
            </div>

            <div className="space-y-6">
              {[
                {
                  question: "How does the prompt enhancement work?",
                  answer: "Our AI analyzes your basic prompt and expands it with relevant details like composition, lighting, style elements, and technical specifications to create professional-quality prompts."
                },
                {
                  question: "Which AI image generators work with these prompts?",
                  answer: "Our enhanced prompts are compatible with all major AI image generators including DALL-E, Midjourney, Stable Diffusion, and others. The prompts include universal syntax and keywords."
                },
                {
                  question: "Do I need to be specific in my raw prompt?",
                  answer: "Not necessarily! Even simple ideas like 'cat in a garden' can be transformed into detailed, professional prompts. However, more specific input often leads to better results."
                },
                {
                  question: "How important is the style selection?",
                  answer: "Very important! The style selection determines the artistic direction and technical approach of your enhanced prompt, significantly affecting the final image quality and aesthetic."
                },
                {
                  question: "Can I edit the generated prompt?",
                  answer: "Absolutely! The generated prompt is fully editable. You can copy it and make any adjustments to better match your specific vision or requirements."
                },
                {
                  question: "What makes a good raw prompt?",
                  answer: "Good raw prompts include the main subject, basic setting, and any specific elements you want. Even simple descriptions work well - our AI will add the professional details."
                }
              ].map((faq, index) => (
                <FAQItem
                  key={index}
                  question={faq.question}
                  answer={faq.answer}
                  isOpen={openFAQ === index}
                  onClick={() => setOpenFAQ(openFAQ === index ? -1 : index)}
                />
              ))}
            </div>
          </div>

          {/* CTA Section */}
          <div className="max-w-4xl mx-auto">
            <div className="bg-gradient-to-r from-[#1a1a23] to-purple-950/50 border border-purple-500/20 rounded-3xl p-8 md:p-12 text-center relative overflow-hidden">
              {/* Background decoration */}
              <div className="absolute top-0 left-0 w-full h-full bg-[radial-gradient(circle_at_50%_0%,rgba(147,51,234,0.15),transparent_50%)]"></div>

              <div className="relative z-10">
                <h2 className="text-2xl md:text-3xl font-bold text-white mb-4">Need More Help?</h2>
                <p className="text-gray-400 mb-8 max-w-lg mx-auto">
                  Can't find the answer you're looking for? Our support team is ready to assist you with any questions about Image Prompt Generator.
                </p>
                <button className="bg-gray-200 hover:bg-white text-gray-900 font-semibold py-3 px-8 rounded-lg transition-colors duration-200 inline-flex items-center gap-2">
                  <MessageCircle className="w-5 h-5" />
                  Contact Support
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ImagePromptGenerator;
