import React, { useState, useRef, useEffect } from 'react';
import { Upload, Download, Image as ImageIcon, Loader2, Check, ArrowRight, ArrowLeft } from 'lucide-react';
import axios from 'axios';
import SEO, { pageSEO } from '../components/SEO';

const MOCK_ASSET = {
  transparent: "https://raw.githubusercontent.com/PokeAPI/sprites/master/sprites/pokemon/other/official-artwork/25.png",
  original_bg_color: "#fca5a5"
};

const BackgroundRemove = () => {
  const [view, setView] = useState('upload'); // upload, processing, editor
  const [selectedFile, setSelectedFile] = useState(null);
  const [processedImage, setProcessedImage] = useState(null);
  const [bgColor, setBgColor] = useState('transparent');
  const [isDownloading, setIsDownloading] = useState(false);
  const [isDraggingOver, setIsDraggingOver] = useState(false);
  const [error, setError] = useState('');

  // Left side slider state
  const [sliderPosition, setSliderPosition] = useState(50);
  const [isDragging, setIsDragging] = useState(false);
  const sliderRef = useRef(null);

  // Colors for the editor
  const colors = [
    { id: 'trans', value: 'transparent', label: 'Transparent' },
    { id: 'white', value: '#ffffff', label: 'White' },
    { id: 'black', value: '#000000', label: 'Black' },
    { id: 'red', value: '#ef4444', label: 'Red' },
    { id: 'orange', value: '#f97316', label: 'Orange' },
    { id: 'yellow', value: '#eab308', label: 'Yellow' },
    { id: 'green', value: '#22c55e', label: 'Green' },
    { id: 'teal', value: '#14b8a6', label: 'Teal' },
    { id: 'blue', value: '#3b82f6', label: 'Blue' },
    { id: 'indigo', value: '#6366f1', label: 'Indigo' },
    { id: 'purple', value: '#a855f7', label: 'Purple' },
    { id: 'pink', value: '#ec4899', label: 'Pink' },
  ];

  const handleFileUpload = (e) => {
    const file = e.target.files?.[0];
    if (file) {
      if (isValidImageFile(file)) {
        startProcessing(URL.createObjectURL(file));
      }
    }
  };

  // Validate image file type and show error for unsupported formats
  const isValidImageFile = (file) => {
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];

    // Check if file type is in allowed list
    if (!allowedTypes.includes(file.type)) {
      alert('❌ Unsupported file format. Please upload JPG, JPEG, PNG, or WebP images only.');
      return false;
    }

    // Check file size (max 10MB)
    const maxSize = 10 * 1024 * 1024; // 10MB in bytes
    if (file.size > maxSize) {
      alert('❌ File size too large. Please upload images smaller than 10MB.');
      return false;
    }

    return true;
  };

  // Drag and Drop handlers
  const handleDragOver = (e) => {
    e.preventDefault();
    setIsDraggingOver(true);
  };

  const handleDragLeave = (e) => {
    e.preventDefault();
    setIsDraggingOver(false);
  };

  const handleDrop = (e) => {
    e.preventDefault();
    setIsDraggingOver(false);
    const file = e.dataTransfer.files?.[0];
    if (file && isValidImageFile(file)) {
      startProcessing(URL.createObjectURL(file));
    }
  };

  const startProcessing = async (imgUrl) => {
    setSelectedFile(imgUrl);
    setView('processing');
    setError('');

    try {
      // Convert image to base64 for API
      const response = await fetch(imgUrl);
      const blob = await response.blob();
      const reader = new FileReader();

      reader.onloadend = async () => {
        const base64data = reader.result;

        try {
          const apiResponse = await axios.post(`${import.meta.env.VITE_API_BASE_URL}/api/ai/background-remove`, {
            imageData: base64data
          });

          if (apiResponse.data.success) {
            setProcessedImage(apiResponse.data.processedImageUrl);
            setView('editor');
          } else {
            throw new Error(apiResponse.data.error || 'Failed to process image');
          }
        } catch (error) {
          console.error('API Error:', error);
          setError(error.response?.data?.error || 'Failed to remove background');
          setView('upload');
        }
      };

      reader.readAsDataURL(blob);
    } catch (error) {
      console.error('Processing Error:', error);
      setError('Failed to process image');
      setView('upload');
    }
  };

  const handleDownload = () => {
    if (!processedImage || isDownloading) return;
    setIsDownloading(true);

    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const img = new Image();

    // Handle cross-origin for the demo image vs local blob
    if (processedImage && !processedImage.startsWith('blob:')) {
      img.crossOrigin = "anonymous";
    }

    img.onload = () => {
      canvas.width = img.naturalWidth;
      canvas.height = img.naturalHeight;

      ctx.clearRect(0, 0, canvas.width, canvas.height);

      // Draw Background Color FIRST (Painter's Algorithm)
      if (bgColor && bgColor !== 'transparent') {
        ctx.fillStyle = bgColor;
        ctx.fillRect(0, 0, canvas.width, canvas.height);
      }

      // Draw the Image ON TOP
      ctx.drawImage(img, 0, 0);

      try {
        const dataUrl = canvas.toDataURL('image/png');
        const link = document.createElement('a');
        link.download = `edited-image-${Date.now()}.png`;
        link.href = dataUrl;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      } catch (err) {
        console.error("Canvas export failed:", err);
        alert("Could not download edited image due to browser security restrictions.");
      }
      setIsDownloading(false);
    };

    img.onerror = () => {
      console.error("Failed to load image for download");
      setIsDownloading(false);
      alert("Failed to load image for download.");
    };

    img.src = processedImage;
  };

  const handleSliderMove = (e) => {
    if (!sliderRef.current) return;
    const rect = sliderRef.current.getBoundingClientRect();
    const x = ('touches' in e ? e.touches[0].clientX : e.clientX) - rect.left;
    const percentage = Math.max(0, Math.min(100, (x / rect.width) * 100));
    setSliderPosition(percentage);
  };

  useEffect(() => {
    const handleUp = () => setIsDragging(false);
    const handleMove = (e) => {
      if (isDragging) handleSliderMove(e);
    };

    window.addEventListener('mouseup', handleUp);
    window.addEventListener('mousemove', handleMove);
    window.addEventListener('touchend', handleUp);
    window.addEventListener('touchmove', handleMove);

    return () => {
      window.removeEventListener('mouseup', handleUp);
      window.removeEventListener('mousemove', handleMove);
      window.removeEventListener('touchend', handleUp);
      window.removeEventListener('touchmove', handleMove);
    };
  }, [isDragging]);



  return (
    <div className="min-h-screen bg-gray-950 text-gray-100 flex flex-col font-sans selection:bg-purple-500 selection:text-white">
      <SEO {...pageSEO.backgroundRemove} />

      {/* Header Section */}
      <div className="w-full py-12 px-4 text-center bg-gray-950 z-10 relative">
        <h1 className="text-4xl md:text-5xl font-bold text-white mb-4 tracking-tight">Free Background Remover</h1>
        <p className="text-gray-400 text-lg max-w-2xl mx-auto leading-relaxed">
          Remove backgrounds from photos quickly and precisely with <span className="text-purple-400 font-medium">genfreeai</span> AI-powered background eraser.
        </p>
      </div>

      {/* Main Content Area */}
      <div className="flex-1 flex flex-col">

        {/* Comparison Slider Section */}
        <div className="w-full bg-gray-950 flex items-center justify-center p-8 relative overflow-hidden min-h-[500px]">
          <div className="absolute inset-0 opacity-10 pointer-events-none">
            <div className="absolute inset-0" style={{ backgroundImage: 'radial-gradient(#374151 1px, transparent 1px)', backgroundSize: '24px 24px' }}></div>
          </div>

          <div
            className="relative w-full max-w-2xl rounded-2xl overflow-hidden shadow-2xl border border-gray-700 bg-gray-800 select-none"
            style={{ aspectRatio: '1500/1080' }}
          >
            {/* Base Layer (Checkerboard) */}
            <div className="absolute inset-0 z-0 bg-white"
               style={{
                 backgroundImage: 'linear-gradient(45deg, #ccc 25%, transparent 25%), linear-gradient(-45deg, #ccc 25%, transparent 25%), linear-gradient(45deg, transparent 75%, #ccc 75%), linear-gradient(-45deg, transparent 75%, #ccc 75%)',
                 backgroundSize: '20px 20px',
                 backgroundPosition: '0 0, 0 10px, 10px -10px, -10px 0px'
               }}>
            </div>

            {/* After Image */}
            <div className="absolute inset-0 z-10 flex items-center justify-center">
               <img src={processedImage || MOCK_ASSET.transparent} alt="After" className="w-[80%] h-[80%] object-contain drop-shadow-xl" draggable="false"/>

               {/* Processing Overlay for After Side */}
               {view === 'processing' && !processedImage && (
                 <div className="absolute inset-0 bg-gray-900/80 backdrop-blur-sm flex items-center justify-center pointer-events-none z-10">
                   <div className="flex flex-col items-center space-y-3">
                     <div className="w-8 h-8 border-2 border-purple-500 border-t-transparent rounded-full animate-spin"></div>
                     <span className="text-white text-xs font-medium">Processing...</span>
                   </div>
                 </div>
               )}

               <span className="absolute bottom-4 right-4 bg-gray-900/80 backdrop-blur text-white px-3 py-1 rounded-full text-sm font-medium border border-gray-700">
                 {view === 'processing' && !processedImage ? 'Processing' : 'After'}
               </span>
            </div>

            {/* Before Image (Clipped) */}
            <div
              className="absolute inset-0 z-20 overflow-hidden bg-gray-900"
              style={{ clipPath: `inset(0 ${100 - sliderPosition}% 0 0)` }}
            >
               <div className="absolute inset-0 flex items-center justify-center">
                   <img src={selectedFile || MOCK_ASSET.transparent} alt="Before" className="w-[80%] h-[80%] object-contain drop-shadow-xl" draggable="false"/>
               </div>
               <span className="absolute bottom-4 left-4 bg-gray-900/80 backdrop-blur text-white px-3 py-1 rounded-full text-sm font-medium border border-gray-700">Before</span>
            </div>

            {/* Slider Handle */}
            <div
              ref={sliderRef}
              className="absolute inset-0 z-30 cursor-ew-resize touch-none"
              onMouseDown={() => setIsDragging(true)}
              onTouchStart={() => setIsDragging(true)}
            >
              <div
                className="absolute top-0 bottom-0 w-1 bg-white cursor-ew-resize shadow-[0_0_10px_rgba(0,0,0,0.5)]"
                style={{ left: `${sliderPosition}%` }}
              >
                <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-8 h-8 bg-white rounded-full flex items-center justify-center shadow-lg transform active:scale-95 transition-transform text-gray-900">
                  <div className="flex gap-0.5">
                     <ArrowLeft size={10} />
                     <ArrowRight size={10} />
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="absolute bottom-8 text-gray-500 text-sm font-medium">
            Drag slider to compare
          </div>
        </div>

        {/* Interface Section */}
        <div className="w-full flex items-center justify-center p-4 md:p-12 relative bg-gray-950 min-h-[500px]">

          {/* UPLOAD VIEW */}
          {view === 'upload' && (
            <div className="w-full max-w-2xl animate-in fade-in slide-in-from-bottom-4 duration-500">
              <div className="bg-gray-900 rounded-3xl p-1 shadow-2xl border border-gray-800">
                <div
                  className={`border-2 border-dashed rounded-[20px] p-8 flex flex-col items-center justify-center text-center space-y-6 transition-all duration-200
                    ${isDraggingOver
                      ? 'border-purple-500 bg-gray-800/80 scale-[1.02] shadow-xl shadow-purple-500/10'
                      : 'border-gray-700 hover:bg-gray-800/50'}`}
                  onDragOver={handleDragOver}
                  onDragLeave={handleDragLeave}
                  onDrop={handleDrop}
                >
                  <div className={`flex -space-x-4 mb-2 transition-opacity duration-300 ${isDraggingOver ? 'opacity-50' : 'opacity-100'}`}>
                     {[1,2,3].map(i => (
                       <div key={i} className="w-12 h-12 rounded-full border-4 border-gray-900 bg-gray-700 overflow-hidden relative">
                          <img
                            src={`https://api.dicebear.com/7.x/avataaars/svg?seed=${i*13}`}
                            className="w-full h-full object-cover"
                            alt="avatar"
                          />
                       </div>
                     ))}
                  </div>

                  <h2 className="text-2xl font-bold text-white">
                    {isDraggingOver ? 'Drop image here' : 'Pick an image'}
                  </h2>
                  <p className="text-gray-400 text-sm max-w-xs pointer-events-none">
                    {isDraggingOver ? 'Release to start processing automatically' : 'Upload an image to remove the background automatically, or drag and drop.'}
                  </p>

                  <div className={`flex flex-col gap-3 w-full transition-opacity duration-300 ${isDraggingOver ? 'opacity-0' : 'opacity-100'}`}>
                    <label className="group relative w-full flex items-center justify-center py-3.5 px-6 border border-transparent rounded-full text-white bg-purple-600 hover:bg-purple-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 cursor-pointer transition-all shadow-lg hover:shadow-purple-500/25">
                       <span className="flex items-center gap-2 font-semibold">
                         <Upload size={20} /> Browse files
                       </span>
                       <input type="file" className="hidden" accept="image/jpeg,image/jpg,image/png,image/webp" onChange={handleFileUpload} />
                    </label>
                  </div>

                  <p className="text-xs text-gray-500 pt-4">
                    Supported formats: JPG, JPEG, PNG, WebP (max 10MB)<br/>
                    By uploading a file, you agree to our <span className="underline hover:text-gray-300 cursor-pointer">Terms of Use</span>.
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* PROCESSING VIEW */}
          {view === 'processing' && (
            <div className="bg-gray-900 rounded-3xl p-12 shadow-2xl border border-gray-800 flex flex-col items-center animate-in zoom-in-95 duration-300">
               <div className="relative">
                 <div className="absolute inset-0 bg-purple-500 blur-xl opacity-20 rounded-full"></div>
                 <Loader2 className="w-16 h-16 text-purple-500 animate-spin relative z-10" />
               </div>
               <h3 className="mt-6 text-xl font-semibold text-white">Processing your image...</h3>
               <p className="text-gray-400 mt-2 text-sm">Removing background magic ✨</p>
            </div>
          )}

          {/* EDITOR VIEW */}
          {view === 'editor' && (
            <div className="w-full max-w-3xl bg-gray-900 rounded-3xl shadow-2xl border border-gray-800 overflow-hidden animate-in fade-in zoom-in-95 duration-300 flex flex-col h-[600px] md:h-auto">

               <div className="p-4 border-b border-gray-800 flex justify-between items-center">
                 <button onClick={() => setView('upload')} className="p-2 hover:bg-gray-800 rounded-full text-gray-400 hover:text-white transition-colors">
                   <ArrowLeft size={20} />
                 </button>
                 <div className="flex gap-2">
                   <button className="p-2 bg-gray-800 rounded-full text-gray-400 hover:text-white">
                     <ImageIcon size={18} />
                   </button>
                 </div>
               </div>

               {/* Canvas Area */}
               <div className="flex-1 relative bg-gray-800 overflow-hidden flex items-center justify-center p-8 group">
                  <div className="absolute inset-0 z-0 opacity-50"
                    style={{
                      backgroundImage: 'linear-gradient(45deg, #374151 25%, transparent 25%), linear-gradient(-45deg, #374151 25%, transparent 25%), linear-gradient(45deg, transparent 75%, #374151 75%), linear-gradient(-45deg, transparent 75%, #374151 75%)',
                      backgroundSize: '20px 20px',
                      backgroundColor: '#1f2937'
                    }}>
                  </div>

                  {/* Selected Color Layer */}
                  <div
                    className="absolute inset-0 z-10 transition-colors duration-300"
                    style={{ backgroundColor: bgColor }}
                  ></div>

                  {/* The Image */}
                  <div className="relative z-20 w-full h-full flex items-center justify-center">
                    <img
                      src={processedImage || MOCK_ASSET.transparent}
                      alt="Processed"
                      className="max-w-full max-h-full object-contain drop-shadow-2xl transition-transform duration-300 group-hover:scale-105"
                    />
                  </div>
               </div>

               {/* Toolbar */}
               <div className="bg-gray-900 p-6 space-y-6 z-30 border-t border-gray-800">
                  <div className="space-y-3">
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium text-gray-400">Background Color</span>
                      <span className="text-xs text-gray-600">{colors.find(c => c.value === bgColor)?.label}</span>
                    </div>

                    <div className="flex gap-3 overflow-x-auto pb-2 scrollbar-hide mask-fade-right">
                      {/* Transparent Button */}
                      <button
                        onClick={() => setBgColor('transparent')}
                        className={`min-w-[40px] h-[40px] rounded-full border-2 flex items-center justify-center transition-all ${bgColor === 'transparent' ? 'border-purple-500 scale-110' : 'border-gray-700 hover:border-gray-500'}`}
                      >
                        <div className="w-full h-full rounded-full overflow-hidden bg-white/10 relative">
                           <div className="absolute inset-0 bg-red-500 w-[2px] h-[150%] left-1/2 -top-1/4 -rotate-45 transform origin-center"></div>
                        </div>
                      </button>

                      {colors.filter(c => c.id !== 'trans').map((color) => (
                        <button
                          key={color.id}
                          onClick={() => setBgColor(color.value)}
                          className={`min-w-[40px] h-[40px] rounded-full border-2 transition-all relative ${bgColor === color.value ? 'border-purple-500 scale-110 shadow-lg shadow-purple-900/20' : 'border-transparent hover:scale-105'}`}
                          style={{ backgroundColor: color.value }}
                        >
                           {bgColor === color.value && (
                             <span className="absolute inset-0 flex items-center justify-center">
                               <Check size={16} className={['white', 'transparent', 'yellow'].includes(color.id) ? 'text-black' : 'text-white'} />
                             </span>
                           )}
                        </button>
                      ))}
                    </div>
                  </div>

                  {/* Download Action */}
                  <div className="flex gap-3 pt-2">
                     <button
                        onClick={handleDownload}
                        disabled={isDownloading}
                        className="flex-1 py-3 px-4 rounded-xl bg-purple-600 hover:bg-purple-500 disabled:bg-purple-800 disabled:opacity-50 text-white font-bold flex items-center justify-center gap-2 shadow-lg shadow-purple-900/50 transition-all hover:scale-[1.02]"
                      >
                        {isDownloading ? <Loader2 size={18} className="animate-spin" /> : <Download size={18} />}
                        {isDownloading ? 'Saving...' : 'Download'}
                     </button>
                  </div>

               </div>
            </div>
          )}

        </div>
      </div>
    </div>
  );
};

export default BackgroundRemove;
