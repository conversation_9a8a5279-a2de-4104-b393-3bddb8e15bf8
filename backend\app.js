const express = require('express');
const mongoose = require('mongoose');
const app = express();
const aiRouter = require('./src/routers/ai.router');
const toolsRouter = require('./src/routers/tools.router');
const cors = require('cors');

// Load environment variables
require('dotenv').config();

// MongoDB connection using environment variable
const mongoURI = process.env.MONGODB_URI;

if (!mongoURI) {
    console.error('❌ MONGODB_URI environment variable is not set');
    process.exit(1);
}

console.log('🔗 Connecting to MongoDB Atlas...');

mongoose.connect(mongoURI)
.then(() => {
    console.log('✅ Connected to MongoDB Atlas');
})
.catch(err => {
    console.error('❌ MongoDB connection error:', err.message);
});

app.use(cors());
app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ extended: true, limit: '50mb' }));

// Routes
app.use('/api/ai', aiRouter);
app.use('/api/tools', toolsRouter);
app.use('/api/admin', require('./routes/admin'));

module.exports = app;